package com.fx.green.electricity.shanxi.server.service.file.impl;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.api.file.VppFileApi;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import com.fx.green.electricity.shanxi.server.service.file.VppFileService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文件上传 Service 实现
 *
 * <AUTHOR>
 */
@Service
public class VppFileServiceImpl implements VppFileService {

    @Resource
    private VppFileApi vppFileApi;

    @Override
    public DataResult<VppFileUploadVO> uploadFile(MultipartFile file) {
        return vppFileApi.uploadFile(file);
    }

    @Override
    public DataResult<List<VppFileUploadVO>> uploadFiles(MultipartFile[] files) {
        return vppFileApi.uploadFiles(files);
    }
}
