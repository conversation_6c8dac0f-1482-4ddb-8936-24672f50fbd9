package com.fx.green.electricity.shanxi.server.service.data;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.List;

/**
 * 数据维护 Service 接口
 */
public interface DataMaintenanceService {

    /**
     * 查询数据维护列表（月）
     *
     * @param param 数据维护DTO
     * @return 数据维护列表
     */
    DataResult<List<DataMaintenanceVO>> queryList(DataMaintenanceDTO param);

    /**
     * 零售合同 - 导入
     *
     * @param file 文件
     * @param runMonth 运行月份
     * @return 结果
     */
    DataResult<Void> importRetailContract(MultipartFile file, String runMonth);

    /**
     * 获取即将失效用户
     *
     * @param param 数据维护DTO
     * @return 即将失效用户列表
     */
    DataResult<List<ExpireUserVO>> getExpireUserList(DataMaintenanceDTO param);


    /**
     * 查询实际用电量详情
     *
     * @param param 数据维护DTO
     * @return 实际用电量详情列表
     */
    DataResult<List<ElectricityDetailVO>> getElectricDetail(DataMaintenanceQueryDTO param);


    /**
     * 删除实际用电量
     *
     * @param param 数据维护DTO
     * @return 结果
     */
    DataResult<Void> delElectric(DataMaintenanceQueryDTO param);

    /**
     * 更新数据维护状态
     *
     * @param param 数据维护DTO
     * @return 结果
     */
    DataResult<Void> updateStatus(DataMaintenanceUpdateDTO param);

    /**
     * 导入实际用电量
     *
     * @param file 文件
     * @return 结果
     */
    DataResult<Void> importElectric(MultipartFile file) throws IOException;

    /**
     * 获取生效的用户的数量
     * @param param 数据维护DTO
     * @return 生效的用户的数量
     */
    DataResult<Integer> geUserCount(DataMaintenanceDTO param);

}
