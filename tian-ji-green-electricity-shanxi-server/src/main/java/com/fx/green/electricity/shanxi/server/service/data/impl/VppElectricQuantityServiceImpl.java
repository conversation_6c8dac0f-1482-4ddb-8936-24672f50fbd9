package com.fx.green.electricity.shanxi.server.service.data.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.EasyExcelFactory;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.constant.TimeConstant;
import com.fx.common.enums.CommonCodeEnum;
import com.fx.common.excel.vo.ImportExcelDetailVO;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.api.VppApi;
import com.fx.green.electricity.shanxi.api.api.file.VppFileApi;
import com.fx.green.electricity.shanxi.api.enums.VirtualPowerPlantServiceCodeEnum;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.VppElectricQuantityImportListDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.GetUpdateTimeDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricActualDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import com.fx.green.electricity.shanxi.api.utils.FileTypeVerifyUtil;
import com.fx.green.electricity.shanxi.server.service.data.VppElectricQuantityService;
import com.fx.green.electricity.shanxi.server.utils.DownloadUtil;
import com.fx.green.electricity.shanxi.server.utils.HttpServletUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class VppElectricQuantityServiceImpl implements VppElectricQuantityService {

    @Autowired
    private VppApi vppApi;

    @Resource
    private VppFileApi vppFileApi;


    @Override
    public DataResult<ImportExcelVO> importElectric(MultipartFile file, String time) throws IOException {
        // 校验文件类型
        boolean checkExcelFileFlag = FileTypeVerifyUtil.checkExcelFile(file.getOriginalFilename());
        if (!checkExcelFileFlag) {
            throw new FxServiceException("请上传xls或xlsx类型文件");
        }


        ImportExcelVO result = new ImportExcelVO();
        result.setDetailList(new ArrayList<>());
        result.setSuccessNum(0);
        result.setFailedNum(1);


        //上传文件
        DataResult<VppFileUploadVO> fileUploadVODataResult = vppFileApi.uploadFile(file);
        String url = fileUploadVODataResult.getData().getUrl();

        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();


        VppElectricQuantityDTO vppElectricQuantityDTO = new VppElectricQuantityDTO();
        vppElectricQuantityDTO.setName(file.getOriginalFilename());
        vppElectricQuantityDTO.setType(2);
        vppElectricQuantityDTO.setUrl(url);
        vppElectricQuantityDTO.setTenantId(tenantId);
        vppElectricQuantityDTO.setUploadTime(new Date());
        vppElectricQuantityDTO.setStatus(Boolean.TRUE);


        List<List<String>> importActualList;

        try {
            //处理数据
            InputStream inputStream = file.getInputStream();
            List<HashMap<Integer, String>> list = EasyExcelFactory.read(inputStream).sheet().headRowNumber(0).doReadSync();
            importActualList = list.stream()
                    .map(map -> new ArrayList<>(map.values()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            ImportExcelDetailVO importDetails = new ImportExcelDetailVO();
            importDetails.setFilename(file.getOriginalFilename());
            importDetails.setInfoList(new ArrayList<>());
            importDetails.setImportDate(new Date());
            importDetails.setStatus(false);
            importDetails.setMessage(VirtualPowerPlantServiceCodeEnum.IMPORT_FILE_TYPE_ERROR.getMessage());
            result.getDetailList().add(importDetails);
            return DataResult.success(CommonCodeEnum.UPLOAD_FAIL, result);
        }


        //表头
        List<String> firstList = importActualList.get(0);
        List<String> timeFrameList = firstList.subList(7, firstList.size())
                .stream()
                .map(Object::toString)
                .collect(Collectors.toList());
        for (int i = 1; i < importActualList.size(); i++) {
            int size = importActualList.get(i).size();
            if (size < firstList.size()) {
                importActualList.remove(i);
            }
        }
        List<VppElectricActualDTO> dataList = new ArrayList<>();
        for (int i = 1; i < importActualList.size(); i++) {
            List<String> currList = importActualList.get(i);
            for (int j = 0; j < currList.size(); j++) {
                VppElectricActualDTO vppDecomposeLoadDTO = new VppElectricActualDTO();
                if (j > 6) {
                    vppDecomposeLoadDTO.setName(currList.get(0));
                    vppDecomposeLoadDTO.setRegistered(currList.get(1));
                    vppDecomposeLoadDTO.setDateDay(DateUtil.parseDate(currList.get(3)));
                    String timeFrame = timeFrameList.get(j - 7);
                    String load = currList.get(j);
                    vppDecomposeLoadDTO.setElectricity(new BigDecimal(load));
                    vppDecomposeLoadDTO.setTimeFrame(timeFrame);
                    dataList.add(vppDecomposeLoadDTO);
                }
            }

        }


        // 判断空
        if (dataList.isEmpty()) {
            ImportExcelDetailVO importDetails = new ImportExcelDetailVO();
            importDetails.setFilename(file.getOriginalFilename());
            importDetails.setInfoList(new ArrayList<>());
            importDetails.setImportDate(new Date());
            importDetails.setStatus(false);
            importDetails.setMessage(VirtualPowerPlantServiceCodeEnum.IMPORT_FILE_TYPE_ERROR.getMessage());
            result.getDetailList().add(importDetails);
            importDetails.setMessage(VirtualPowerPlantServiceCodeEnum.IMPORT_EMPTY_ERROR.getMessage());
            return DataResult.success(CommonCodeEnum.UPLOAD_FAIL, result);
        }

        //判断导入数量是否为96的倍数
        if (dataList.size() % TimeConstant.NINETY_SIX_NUMBER != 0) {
            ImportExcelDetailVO importDetails = new ImportExcelDetailVO();
            importDetails.setFilename(file.getOriginalFilename());
            importDetails.setInfoList(new ArrayList<>());
            importDetails.setImportDate(new Date());
            importDetails.setStatus(false);
            importDetails.setMessage(VirtualPowerPlantServiceCodeEnum.IMPORT_FILE_TYPE_ERROR.getMessage());
            result.getDetailList().add(importDetails);
            importDetails.setMessage(VirtualPowerPlantServiceCodeEnum.IMPORT_EMPTY_ERROR.getMessage());
            importDetails.setMessage(VirtualPowerPlantServiceCodeEnum.IMPORT_QUANTITY_ERROR.getMessage());
            return DataResult.success(CommonCodeEnum.UPLOAD_FAIL, result);
        }

        Map<String, List<VppElectricActualDTO>> dataMap = dataList.stream().collect(Collectors.groupingBy(o -> DateUtil.formatDate(o.getDateDay())));

        List<VppElectricActualDTO> finalList = dataMap.entrySet().stream()
                .flatMap(dateEntry -> dateEntry.getValue().stream()
                        .collect(Collectors.groupingBy(VppElectricActualDTO::getName))
                        .entrySet().stream()
                        .flatMap(userEntry -> userEntry.getValue().stream()
                                .collect(Collectors.groupingBy(VppElectricActualDTO::getTimeFrame))
                                .entrySet().stream()
                                .map(timeEntry -> {
                                    List<VppElectricActualDTO> timeList = timeEntry.getValue();
                                    BigDecimal electricity = timeList.stream()
                                            .map(VppElectricActualDTO::getElectricity)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                                    VppElectricActualDTO vppElectricActualDTO = new VppElectricActualDTO();
                                    vppElectricActualDTO.setTimeFrame(timeEntry.getKey());
                                    vppElectricActualDTO.setElectricity(electricity);
                                    vppElectricActualDTO.setUserCode(timeList.get(0).getUserCode());
                                    vppElectricActualDTO.setRegistered(timeList.get(0).getRegistered());
                                    vppElectricActualDTO.setDateDay(timeList.get(0).getDateDay());
                                    vppElectricActualDTO.setName(timeList.get(0).getName());
                                    return vppElectricActualDTO;
                                })))
                .collect(Collectors.toList());


        Map<String, List<VppElectricActualDTO>> finalMap = finalList.stream().collect(Collectors.groupingBy(o -> DateUtil.formatDate(o.getDateDay())));

        for (Map.Entry<String, List<VppElectricActualDTO>> entry : finalMap.entrySet()) {
            ImportExcelDetailVO importDetail = new ImportExcelDetailVO();
            importDetail.setFilename(file.getOriginalFilename());
            importDetail.setInfoList(new ArrayList<>());
            importDetail.setImportDate(new Date());
            importDetail.setStatus(false);


            String key = entry.getKey();
            vppElectricQuantityDTO.setRunningDate(DateUtil.parse(key));
            List<VppElectricActualDTO> vppElectricActualDTOS = finalMap.get(key);
            vppElectricQuantityDTO.setElectricActualList(vppElectricActualDTOS);
            DataResult<ImportExcelDetailVO> dataResult = vppApi.vppElectricQuantityImportElectric(vppElectricQuantityDTO);
            if (dataResult.getStatus() != CommonCodeEnum.UPLOAD_SUCCESS.getCode()) {
                importDetail.setMessage(dataResult.getMessage());
                if (ObjectUtil.isNotNull(dataResult.getData())) {
                    BeanUtils.copyProperties(dataResult.getData(), importDetail);
                }
                return DataResult.success(CommonCodeEnum.UPLOAD_FAIL, result);
            }

            ImportExcelDetailVO data = dataResult.getData();
            importDetail.setInfoList(data.getInfoList());
            importDetail.setMessage(data.getMessage());
            importDetail.setStatus(data.getStatus());
            importDetail.setFailedNum(data.getFailedNum());
            importDetail.setSuccessNum(data.getSuccessNum());
            if (Boolean.TRUE.equals(importDetail.getStatus())) {
                result.setSuccessNum(1);
                result.setFailedNum(0);
            }
            importDetail.setFilename(key + file.getOriginalFilename());
            result.getDetailList().add(importDetail);
        }
        return DataResult.success(CommonCodeEnum.UPLOAD_SUCCESS, result);
    }


    @Override
    public DataResult<ImportExcelVO> importElectricNew(MultipartFile file, String time) throws IOException {
        // 校验文件类型
        boolean checkExcelFileFlag = FileTypeVerifyUtil.checkExcelFile(file.getOriginalFilename());
        if (!checkExcelFileFlag) {
            throw new FxServiceException("请上传xls或xlsx类型文件");
        }


        ImportExcelVO result = new ImportExcelVO();
        result.setDetailList(new ArrayList<>());
        result.setSuccessNum(0);
        result.setFailedNum(1);


        //上传文件
        DataResult<VppFileUploadVO> fileUploadVODataResult = vppFileApi.uploadFile(file);
        String url = fileUploadVODataResult.getData().getUrl();

        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();


        List<List<String>> importActualList;

        try {
            //处理数据
            InputStream inputStream = file.getInputStream();
            List<HashMap<Integer, String>> list = EasyExcelFactory.read(inputStream).sheet().headRowNumber(0).doReadSync();
            importActualList = list.stream()
                    .map(map -> new ArrayList<>(map.values()))
                    .collect(Collectors.toList());

        } catch (Exception e) {
            ImportExcelDetailVO importDetails = new ImportExcelDetailVO();
            importDetails.setFilename(file.getOriginalFilename());
            importDetails.setInfoList(new ArrayList<>());
            importDetails.setImportDate(new Date());
            importDetails.setStatus(false);
            importDetails.setMessage(VirtualPowerPlantServiceCodeEnum.IMPORT_FILE_TYPE_ERROR.getMessage());
            result.getDetailList().add(importDetails);
            return DataResult.success(CommonCodeEnum.UPLOAD_FAIL, result);
        }


        //表头
        List<String> firstList = importActualList.get(0);
        List<String> timeFrameList = firstList.subList(7, firstList.size())
                .stream()
                .map(Object::toString)
                .collect(Collectors.toList());
        for (int i = 1; i < importActualList.size(); i++) {
            int size = importActualList.get(i).size();
            if (size < firstList.size()) {
                importActualList.remove(i);
            }
        }
        List<VppElectricActualDTO> dataList = new ArrayList<>();
        for (int i = 1; i < importActualList.size(); i++) {
            List<String> currList = importActualList.get(i);
            for (int j = 0; j < currList.size(); j++) {
                VppElectricActualDTO vppDecomposeLoadDTO = new VppElectricActualDTO();
                if (j > 6) {
                    vppDecomposeLoadDTO.setName(currList.get(0));
                    vppDecomposeLoadDTO.setRegistered(currList.get(1));
                    vppDecomposeLoadDTO.setDateDay(DateUtil.parseDate(currList.get(3)));
                    String timeFrame = timeFrameList.get(j - 7);
                    String load = currList.get(j);
                    vppDecomposeLoadDTO.setElectricity(new BigDecimal(load));
                    vppDecomposeLoadDTO.setTimeFrame(timeFrame);
                    dataList.add(vppDecomposeLoadDTO);
                }
            }

        }


        // 判断空
        if (dataList.isEmpty()) {
            ImportExcelDetailVO importDetails = new ImportExcelDetailVO();
            importDetails.setFilename(file.getOriginalFilename());
            importDetails.setInfoList(new ArrayList<>());
            importDetails.setImportDate(new Date());
            importDetails.setStatus(false);
            importDetails.setMessage(VirtualPowerPlantServiceCodeEnum.IMPORT_FILE_TYPE_ERROR.getMessage());
            result.getDetailList().add(importDetails);
            importDetails.setMessage(VirtualPowerPlantServiceCodeEnum.IMPORT_EMPTY_ERROR.getMessage());
            return DataResult.success(CommonCodeEnum.UPLOAD_FAIL, result);
        }

        //判断导入数量是否为96的倍数
        if (dataList.size() % TimeConstant.NINETY_SIX_NUMBER != 0) {
            ImportExcelDetailVO importDetails = new ImportExcelDetailVO();
            importDetails.setFilename(file.getOriginalFilename());
            importDetails.setInfoList(new ArrayList<>());
            importDetails.setImportDate(new Date());
            importDetails.setStatus(false);
            importDetails.setMessage(VirtualPowerPlantServiceCodeEnum.IMPORT_FILE_TYPE_ERROR.getMessage());
            result.getDetailList().add(importDetails);
            importDetails.setMessage(VirtualPowerPlantServiceCodeEnum.IMPORT_EMPTY_ERROR.getMessage());
            importDetails.setMessage(VirtualPowerPlantServiceCodeEnum.IMPORT_QUANTITY_ERROR.getMessage());
            return DataResult.success(CommonCodeEnum.UPLOAD_FAIL, result);
        }

        Map<String, List<VppElectricActualDTO>> dataMap = dataList.stream().collect(Collectors.groupingBy(o -> DateUtil.formatDate(o.getDateDay())));

        List<VppElectricActualDTO> finalList = dataMap.entrySet().stream()
                .flatMap(dateEntry -> dateEntry.getValue().stream()
                        .collect(Collectors.groupingBy(VppElectricActualDTO::getRegistered))
                        .entrySet().stream()
                        .flatMap(userEntry -> userEntry.getValue().stream()
                                .collect(Collectors.groupingBy(VppElectricActualDTO::getTimeFrame))
                                .entrySet().stream()
                                .map(timeEntry -> {
                                    List<VppElectricActualDTO> timeList = timeEntry.getValue();
                                    BigDecimal electricity = timeList.stream()
                                            .map(VppElectricActualDTO::getElectricity)
                                            .reduce(BigDecimal.ZERO, BigDecimal::add);

                                    VppElectricActualDTO vppElectricActualDTO = new VppElectricActualDTO();
                                    vppElectricActualDTO.setTimeFrame(timeEntry.getKey());
                                    vppElectricActualDTO.setElectricity(electricity);
                                    vppElectricActualDTO.setUserCode(timeList.get(0).getUserCode());
                                    vppElectricActualDTO.setRegistered(timeList.get(0).getRegistered());
                                    vppElectricActualDTO.setDateDay(timeList.get(0).getDateDay());
                                    vppElectricActualDTO.setName(timeList.get(0).getName());
                                    return vppElectricActualDTO;
                                })))
                .collect(Collectors.toList());


        Map<String, List<VppElectricActualDTO>> finalMap = finalList.stream().collect(Collectors.groupingBy(o -> DateUtil.formatDate(o.getDateDay())));

        List<VppElectricQuantityDTO> resultList = new ArrayList<>();
        for (Map.Entry<String, List<VppElectricActualDTO>> entry : finalMap.entrySet()) {
            VppElectricQuantityDTO vppElectricQuantityDTO = new VppElectricQuantityDTO();
            vppElectricQuantityDTO.setName(file.getOriginalFilename());
            vppElectricQuantityDTO.setType(2);
            vppElectricQuantityDTO.setUrl(url);
            vppElectricQuantityDTO.setTenantId(tenantId);
            vppElectricQuantityDTO.setUploadTime(new Date());
            vppElectricQuantityDTO.setStatus(Boolean.TRUE);
            String key = entry.getKey();
            vppElectricQuantityDTO.setRunningDate(DateUtil.parse(key));
            List<VppElectricActualDTO> vppElectricActualDTOS = entry.getValue();
            vppElectricQuantityDTO.setElectricActualList(vppElectricActualDTOS);
            resultList.add(vppElectricQuantityDTO);
        }
        VppElectricQuantityImportListDTO vppElectricQuantityImportListDTO = new VppElectricQuantityImportListDTO();
        vppElectricQuantityImportListDTO.setList(resultList);
        DataResult<ImportExcelDetailVO> dataResult = vppApi.vppElectricQuantityImportElectricNew(vppElectricQuantityImportListDTO);
        System.out.println(dataResult);
        return DataResult.success();
    }

    @Override
    public DataResult<FxPage<ImportExcelDetailPageListVO>> queryImportRecord(VppElectricQuantityDTO.QueryDTO param) {
        return vppApi.queryImportRecord(param);
    }

    @Override
    public DataResult<ImportExcelVO> importRecord(MultipartFile file, String time) {
        // 校验文件类型
        boolean checkExcelFileFlag = FileTypeVerifyUtil.checkExcelFile(file.getOriginalFilename());
        if (!checkExcelFileFlag) {
            throw new FxServiceException("请上传xls或xlsx类型文件");
        }
        try {
            Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
            //获取虚拟电厂信息
            InputStream inputStream = file.getInputStream();
            List<VppElectricDeclareDTO> importList = EasyExcelFactory.read(inputStream).head(VppElectricDeclareDTO.class).sheet().headRowNumber(1).doReadSync();
            for (VppElectricDeclareDTO vppElectricDeclareDTO : importList) {
                String timeFrame = vppElectricDeclareDTO.getTimeFrame().split("-")[1];
                vppElectricDeclareDTO.setTimeFrame(timeFrame);
                vppElectricDeclareDTO.setDateDay(DateUtil.parse(time));
                vppElectricDeclareDTO.setTenantId(tenantId);
            }

            //上传文件
            DataResult<VppFileUploadVO> fileUploadVODataResult = vppFileApi.uploadFile(file);
            String url = fileUploadVODataResult.getData().getUrl();

            //添加导入信息
            VppElectricQuantityDTO vppElectricQuantityDTO = new VppElectricQuantityDTO();
            vppElectricQuantityDTO.setName(file.getOriginalFilename());
            vppElectricQuantityDTO.setUrl(url);
            vppElectricQuantityDTO.setType(1);
            vppElectricQuantityDTO.setTenantId(tenantId);
            vppElectricQuantityDTO.setRunningDate(DateUtil.parse(time));
            vppElectricQuantityDTO.setUploadTime(new Date());
            vppElectricQuantityDTO.setStatus(Boolean.TRUE);
            vppElectricQuantityDTO.setElectricDeclareList(importList);
            return vppApi.importRecord(vppElectricQuantityDTO);
        } catch (Exception e) {
            log.error("数据解析失败" + e);
            return DataResult.failed("数据解析失败");
        }
    }

    @Override
    public DataResult<String> getUpdateTime(GetUpdateTimeDTO param) {
        return vppApi.getUpdateTime(param);
    }

    @Override
    public DataResult<Void> deleteRecordData(DeleteRecordDTO deleteRecordDTO) {
        return vppApi.deleteRecordData(deleteRecordDTO);
    }

    @Override
    public DataResult<Void> downloadRecord(CommonDTO.DateDTO param) {
        DataResult<List<SeElectricDeclareVO>> listDataResult = vppApi.downloadRecord(param);
        if (ObjectUtil.isNotNull(listDataResult.getData())) {
            List<SeElectricDeclareVO> data = listDataResult.getData();
            String fileName = "日前申报数据" + DateUtil.formatDate(param.getQueryDate()) + ".xlsx";
            ByteArrayOutputStream stream = new ByteArrayOutputStream();
            EasyExcel.write(stream, SeElectricDeclareVO.class)
                    .sheet()
                    .doWrite(data);
            byte[] fileBytes = stream.toByteArray();
            DownloadUtil.downloadJsonType(fileName, fileBytes, HttpServletUtil.getResponse());
        }
        return DataResult.success();
    }
}
