package com.fx.green.electricity.shanxi.server.controller.maintenance;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.GetUpdateTimeDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import com.fx.green.electricity.shanxi.server.service.data.VppElectricQuantityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * 数据维护-电量数据维护
 * <AUTHOR>
 */
@RestController
@Api(tags = "数据维护-电量数据维护")
@RequestMapping("vppElectricQuantity")
public class VppElectricQuantityController {

    @Autowired
    private VppElectricQuantityService vppElectricQuantityService;

    @ApiOperation("电量数据导入")
    @CommonNoRepeat(interval = 3000)
    @PostMapping("importElectric")
    public DataResult<ImportExcelVO> importElectricQuantity(@RequestPart("file") MultipartFile file,
                                                            @RequestParam("time") @ApiParam("运行时间") String time) throws IOException {
        return vppElectricQuantityService.importElectric(file, time);
    }


    @ApiOperation("查询实际用电量导入记录")
    @PostMapping("queryImportRecord")
    public DataResult<FxPage<ImportExcelDetailPageListVO>> queryImportRecord(@RequestBody VppElectricQuantityDTO.QueryDTO param) {
        return vppElectricQuantityService.queryImportRecord(param);
    }


    @ApiOperation("申报数据导入")
    @CommonNoRepeat(interval = 3000)
    @PostMapping("importRecord")
    public DataResult<ImportExcelVO> importRecord(@RequestPart("file") MultipartFile file,
                                                  @RequestParam("time") @ApiParam("运行时间") String time) {
        return vppElectricQuantityService.importRecord(file, time);
    }


    @ApiOperation("获取实际用电量更新时间")
    @PostMapping("getUpdateTime")
    public DataResult<String> getUpdateTime(@RequestBody GetUpdateTimeDTO param) {
        return vppElectricQuantityService.getUpdateTime(param);
    }
}
