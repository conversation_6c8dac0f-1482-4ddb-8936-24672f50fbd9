package com.fx.green.electricity.shanxi.server.controller.retail;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.server.service.retail.VppRetailContractsPriceService;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsPriceQueryVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsPriceVO;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 *零售合同管理浮动价格值
 **/
@RestController
@Api(tags = "零售合同管理浮动价格值")
@RequestMapping("vppRetailContractsPrice")
public class VppRetailContractsPriceController {

    @Resource
    private VppRetailContractsPriceService vppRetailContractsPriceService;


    @ApiOperation("数据列表")
    @PostMapping("dataList")
    public DataResult<List<VppRetailContractsPriceVO>> pageList(@RequestBody VppRetailContractsPriceQueryVO param) {
        return vppRetailContractsPriceService.getPriceList(param);
    }

    @ApiOperation("导出数据")
    @PostMapping("dataDerive")
    public DataResult<Void> dataDerive(@RequestBody VppRetailContractsPriceQueryVO param) {
        return vppRetailContractsPriceService.dataDerive(param);
    }

}
