package com.fx.green.electricity.shanxi.server.service.retail.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.api.VppRetailContractsApi;
import com.fx.green.electricity.shanxi.api.api.file.VppFileApi;
import com.fx.green.electricity.shanxi.api.api.user.VppLoadUserApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryImportRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.GetAllListDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.utils.FileTypeVerifyUtil;
import com.fx.green.electricity.shanxi.server.service.retail.VppRetailContractsService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppRetailContractsServiceImpl implements VppRetailContractsService {

    @Resource
    private VppRetailContractsApi vppRetailContractsApi;

    @Resource
    private VppFileApi vppFileApi;

    @Resource
    private VppLoadUserApi vppLoadUserApi;

    @Override
    public DataResult<Void> queryImportRecord(MultipartFile file, String runMonth, Long retailContractsId, String tenantId) {
        // 校验文件类型
        boolean checkExcelFileFlag = FileTypeVerifyUtil.checkExcelFile(file);
        if (!checkExcelFileFlag) {
            throw new FxServiceException("请上传xls或xlsx类型文件");
        }
        try {
            InputStream inputStream = file.getInputStream();
            List<HashMap<Integer, String>> list = EasyExcelFactory.read(inputStream).sheet().headRowNumber(0).doReadSync();
            List<List<String>> inputList = list.stream()
                    .map(map -> new ArrayList<>(map.values()))
                    .collect(Collectors.toList());

            List<VppRetailContractsManageVO> resultList = new ArrayList<>();
            List<String> firstList = inputList.get(0);
            List<String> timeFrameList = firstList.subList(4, firstList.size())
                    .stream()
                    .map(Object::toString)
                    .collect(Collectors.toList());

            for (int i = 1; i < inputList.size(); i++) {
                List<String> currList = inputList.get(i);

                for (int j = 0; j < currList.size(); j++) {
                    VppRetailContractsManageVO vo = new VppRetailContractsManageVO();

                    vo.setRetailContractsId(retailContractsId);
                    vo.setSigningMonth(DateUtil.parseDate(runMonth + "-01"));

                    String name = null;
                    if (currList.get(0) != null && !currList.get(0).isEmpty()) {
                        name = currList.get(0);
                    } else {
                        name = inputList.get(i - 1).get(0);
                    }
                    vo.setName(name);

                    BigDecimal dividendSharCoefficient = null;
                    if (currList.get(1) != null && !currList.get(1).isEmpty()) {
                        dividendSharCoefficient = new BigDecimal(currList.get(1));
                    } else {
                        dividendSharCoefficient = new BigDecimal(inputList.get(i - 1).get(1));
                    }
                    vo.setDividendSharCoefficient(dividendSharCoefficient);

                    Integer cycle = currList.get(2) != null ? (currList.get(2).equals("按月") ? 1 : null) : null;
                    vo.setCycle(cycle);

                    Integer type = currList.get(3) != null ? (currList.get(3).equals("电量") ? 1 : 2) : null;
                    vo.setType(type);

                    if (j < timeFrameList.size()) {
                        vo.setTimeFrame(timeFrameList.get(j));
                        if (currList.size() > j + 4) {

                            String val = currList.get(j + 4);
                            if (type == 2 && ObjectUtil.isNull(val)) {
                                return DataResult.failed("导入失败，电价数据有空值，请修改后重试！");
                            } else {
                                vo.setVal(new BigDecimal(val));
                                resultList.add(vo);
                            }
                        } else {
                            if (type == 1) {
                                break;
                            } else {
                                return DataResult.failed("导入失败，电价数据有空值，请修改后重试！");
                            }
                        }
                    } else {
                        break;
                    }

                }
            }


            //校验导入的文件中用户是否存在
            Map<String, List<VppRetailContractsManageVO>> collect = resultList.stream().collect(Collectors.groupingBy(VppRetailContractsManageVO::getName));
            List<String> keysList = new ArrayList<>(collect.keySet());
            GetAllListDTO getAllListDTO = new GetAllListDTO();
            getAllListDTO.setKeysList(keysList);
            getAllListDTO.setTenantId(tenantId);
            DataResult<List<VppLoadUserVO>> allList = vppLoadUserApi.getAllList(getAllListDTO);
            List<VppLoadUserVO> userList = allList.getData();
            if (keysList.size() != userList.size()) {
                return DataResult.failed("导入失败，零售合同用户名称不符，请修改后重试！");
            }
            Map<String, Long> map = userList.stream().collect(Collectors.toMap(VppLoadUserVO::getName, VppLoadUserVO::getId));
            for (VppRetailContractsManageVO vppRetailContractsManage : resultList) {
                Long userId = map.get(vppRetailContractsManage.getName());
                vppRetailContractsManage.setUserId(userId);
            }

            //上传文件
            DataResult<VppFileUploadVO> fileUploadVODataResult = vppFileApi.uploadFile(file);
            String name = fileUploadVODataResult.getData().getName();
            String url = fileUploadVODataResult.getData().getUrl();

            QueryImportRecordDTO queryImportRecordDTO = new QueryImportRecordDTO();
            queryImportRecordDTO.setList(resultList);
            queryImportRecordDTO.setName(name);
            queryImportRecordDTO.setUrl(url);
            queryImportRecordDTO.setRetailContractsId(retailContractsId);
            queryImportRecordDTO.setTenantId(Long.valueOf(tenantId));


            return vppRetailContractsApi.queryImportRecord(queryImportRecordDTO);
        } catch (
                Exception e) {
            log.error("数据解析失败" + e);
            return DataResult.failed("数据解析失败");
        }

    }

    @Override
    public DataResult<FxPage<VppRetailContractsVO>> getList(QueryVppRetailContractsDTO param) {
        return vppRetailContractsApi.getList(param);
    }


    /**
     * 获取某个日期的开始时间
     *
     * @param d
     * @return
     */
    public static Date getDayStartTime(Date d) {
        Calendar calendar = Calendar.getInstance();
        if (null != d) calendar.setTime(d);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 0, 0, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        return calendar.getTime();
    }

    /**
     * 获取某个日期的结束时间
     *
     * @param d
     * @return
     */
    public static Date getDayEndTime(Date d) {
        Calendar calendar = Calendar.getInstance();
        if (null != d) calendar.setTime(d);
        calendar.set(calendar.get(Calendar.YEAR), calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH), 23, 59, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        return calendar.getTime();
    }

}
