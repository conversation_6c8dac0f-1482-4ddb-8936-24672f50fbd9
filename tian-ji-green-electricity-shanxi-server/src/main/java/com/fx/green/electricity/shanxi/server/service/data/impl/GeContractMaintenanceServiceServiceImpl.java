package com.fx.green.electricity.shanxi.server.service.data.impl;

import cn.hutool.core.date.DateUtil;
import com.alibaba.excel.EasyExcel;
import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.api.GeMaintenanceApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeContractMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeUnitImportDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeUnitSaveDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.TimeBaseDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeContractMaintenanceVO;
import com.fx.green.electricity.shanxi.api.utils.FileTypeVerifyUtil;
import com.fx.green.electricity.shanxi.server.service.data.GeContractMaintenanceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 合同维护数据
 */
@Service
@Slf4j
public class GeContractMaintenanceServiceServiceImpl implements GeContractMaintenanceService {

    @Autowired
    private GeMaintenanceApi geMaintenanceApi;

    /**
     * 新增合同数据
     *
     * @param param
     */
    @Override
    public DataResult<Void> insertContract(GeContractMaintenanceDTO param) {
        return geMaintenanceApi.insertContract(param);
    }

    /**
     * 删除合同数据
     *
     * @param param
     */
    @Override
    public DataResult<Void> deleteContract(IdDTO param) {
        return geMaintenanceApi.deleteContract(param);
    }

    /**
     * 合同数据分页
     *
     * @param param
     * @return
     */
    @Override
    public DataResult<List<GeContractMaintenanceVO>> contractList(GeContractMaintenanceDTO param) {
        return geMaintenanceApi.contractList(param);
    }

    /**
     * 导入机组实际发电曲线、功率预测、节点价
     * type 1:实际发电曲线  2:功率预测  3:节点价格
     *
     * @param file
     */
    @Override
    public DataResult<Void> importUnitRealElectricityNodePower(MultipartFile file, Integer type) {

        //校验文件类型
        boolean checkExcelFileFlag = FileTypeVerifyUtil.checkExcelFile(file);
        if (!checkExcelFileFlag) {
            throw new FxServiceException("请上传xls或xlsx类型文件");
        }
        try {

            InputStream inputStream = file.getInputStream();
            List<LinkedHashMap<Integer, String>> excelList = EasyExcel.read(inputStream).sheet().headRowNumber(0).doReadSync();
            if (excelList == null || excelList.isEmpty()) {
                return DataResult.failed("文件内容为空");
            }

            // 解析第一行获取时间列头
            LinkedHashMap<Integer, String> headerRow = excelList.get(0);
            List<String> timeHeaders = parseTimeHeaders(headerRow, 3);

            // 解析数据行
            List<GeUnitImportDTO> entities = new ArrayList<>();
            for (int i = 1; i < excelList.size(); i++) {
                LinkedHashMap<Integer, String> dataRow = excelList.get(i);

                // 获取日期（第一列）
                Date dateTime = DateUtil.parseDate(dataRow.get(1));

                // 获取机组名称（第二列）
                String unitName = dataRow.get(2);
                List<TimeBaseDTO> timeBaseDTOS = parseDataRow(dataRow, timeHeaders, 3);
                List<GeUnitImportDTO> unitList = timeBaseDTOS.stream().map(t -> {
                    GeUnitImportDTO geUnitImportDTO = new GeUnitImportDTO();
                    geUnitImportDTO.setValue(t.getValue());
                    geUnitImportDTO.setTimeFrame(t.getTimeFrame());
                    geUnitImportDTO.setUnitName(unitName);
                    geUnitImportDTO.setInfoDate(dateTime);
                    return geUnitImportDTO;
                }).collect(Collectors.toList());
                entities.addAll(unitList);
            }

            GeUnitSaveDTO geUnitSaveDTO = new GeUnitSaveDTO();
            geUnitSaveDTO.setFileType(type);
            geUnitSaveDTO.setFileDataList(entities);
            return geMaintenanceApi.importUnitData(geUnitSaveDTO);
        } catch (IOException e) {
            log.error("导入模板不正确" + e);
            return DataResult.failed("导入模板不正确");
        }
    }

    /**
     * 处理表头获取到96点数据
     *
     * @param headerRow 表头
     * @param jumpNum   跳过非时间表头数
     * @return
     */
    private List<String> parseTimeHeaders(LinkedHashMap<Integer, String> headerRow, Integer jumpNum) {
        List<String> timeHeaders = new ArrayList<>();

        // 跳过前两列（日期和机组名称）
        for (int i = jumpNum; i < headerRow.size(); i++) {
            String header = headerRow.get(i);
            if (header != null && !header.trim().isEmpty()) {
                timeHeaders.add(header.trim());
            }
        }

        return timeHeaders;
    }

    /**
     * 处理文件数据
     *
     * @param dataRow     数据行
     * @param timeHeaders 时间表头
     * @param dataNum     数据行开始数
     * @return
     */
    private List<TimeBaseDTO> parseDataRow(LinkedHashMap<Integer, String> dataRow, List<String> timeHeaders, Integer dataNum) {
        List<TimeBaseDTO> entities = new ArrayList<>();


        // 遍历时间列数据（从第三列开始）
        for (int i = dataNum; i < dataRow.size(); i++) {
            String time = timeHeaders.get(i - dataNum); // 对应的时间点
            String valueStr = dataRow.get(i);

            if (valueStr != null && !valueStr.trim().isEmpty()) {
                try {
                    BigDecimal value = new BigDecimal(valueStr.trim());

                    TimeBaseDTO entity = new TimeBaseDTO();
                    entity.setTimeFrame(time);
                    entity.setValue(value);

                    entities.add(entity);
                } catch (NumberFormatException e) {
                    log.warn("数值格式错误: {}", valueStr);
                }
            }
        }
        return entities;
    }
}
