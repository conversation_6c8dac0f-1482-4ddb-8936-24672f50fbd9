package com.fx.green.electricity.shanxi.server.service.data.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.exception.FxServiceException;
import com.fx.green.electricity.shanxi.api.api.DataMaintenanceApi;
import com.fx.green.electricity.shanxi.api.api.VppRetailContractsApi;
import com.fx.green.electricity.shanxi.api.api.file.VppFileApi;
import com.fx.green.electricity.shanxi.api.constant.VppConstant;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryImportRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.AdjustDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.utils.FileTypeVerifyUtil;
import com.fx.green.electricity.shanxi.server.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.server.service.data.VppElectricQuantityService;
import com.fx.green.electricity.shanxi.server.service.user.VppLoadUserService;
import com.fx.operation.api.api.OperationTransactionScheduleApi;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class DataMaintenanceServiceImpl implements DataMaintenanceService {
    @Resource
    private DataMaintenanceApi dataMaintenanceApi;

    @Resource
    private VppRetailContractsApi vppRetailContractsApi;

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Resource
    private OperationTransactionScheduleApi operationTransactionScheduleApi;

    @Resource
    private VppFileApi vppFileApi;


    @Override
    public DataResult<List<DataMaintenanceVO>> queryList(DataMaintenanceDTO param) {
        return dataMaintenanceApi.queryList(param);
    }

    @Override
    public DataResult<List<ExpireUserVO>> getExpireUserList(DataMaintenanceDTO param) {
        return dataMaintenanceApi.getExpireUserList(param);
    }

    @Override
    public DataResult<Integer> geUserCount(DataMaintenanceDTO param) {
        return dataMaintenanceApi.geUserCount(param);
    }

    @Override
    public DataResult<List<ElectricityDetailVO>> getElectricDetail(DataMaintenanceQueryDTO param) {
        return dataMaintenanceApi.getElectricDetail(param);
    }

    @Override
    public DataResult<Void> delElectric(DataMaintenanceQueryDTO param) {
        return dataMaintenanceApi.delElectric(param);
    }

    @Override
    public DataResult<Void> importRetailContract(MultipartFile file, String runMonth, HttpServletRequest request) {
        // 校验文件类型
        boolean checkExcelFileFlag = FileTypeVerifyUtil.checkExcelFile(file.getOriginalFilename());
        if (!checkExcelFileFlag) {
            throw new FxServiceException("请上传xls或xlsx类型文件");
        }
        String tenantId = request.getHeader("tenantId");
        //获取虚拟电厂当前月份的零售合同id
        QueryVppRetailContractsDTO param = new QueryVppRetailContractsDTO();
        param.setTenantId(Long.valueOf(tenantId));
        param.setStartDate(DateUtil.parseDate(runMonth + "-01"));
        param.setEndDate(DateUtil.endOfMonth(DateUtil.parseDate(runMonth + "-01")));
        DataResult<FxPage<VppRetailContractsVO>> list = vppRetailContractsApi.getList(param);
        List<VppRetailContractsVO> records = list.getData().getRecords();

        //获取本月绑定周期的用户
        AdjustDeclareDTO.QueryUserTreeDTO userTreeDTO = new AdjustDeclareDTO.QueryUserTreeDTO();
        userTreeDTO.setQueryDate(DateUtil.parseDate(runMonth + "-01"));
        userTreeDTO.setTenantId(Long.valueOf(tenantId));
        DataResult<VppLoadUserVO.TreeVO> treeVODataResult = vppLoadUserService.queryTreeList(userTreeDTO);
        Map<String, List<VppLoadUserVO.TreeUserVO>> nameMap = new HashMap<>();
        VppLoadUserVO.TreeVO data = treeVODataResult.getData();
        if (ObjectUtil.isNotNull(data)) {
            List<VppLoadUserVO.TreeUserVO> treeUserVOList = data.getTreeUserVOList();
            nameMap = treeUserVOList.stream().collect(Collectors.groupingBy(VppLoadUserVO.TreeUserVO::getName));
        }


        if (ObjectUtil.isNotEmpty(records)) {
            try {
                InputStream inputStreams = file.getInputStream();
                LinkedHashMap<String, Integer> listMap = new LinkedHashMap<>();
                List<LinkedHashMap<Integer, String>> excelList = EasyExcelFactory.read(inputStreams).headRowNumber(0).doReadAllSync();
                for (int i = 0; i < 1; i++) {
                    LinkedHashMap<Integer, String> map = excelList.get(i);
                    for (Map.Entry<Integer, String> entry : map.entrySet()) {
                        Integer key = entry.getKey();
                        String s = map.get(key);
                        if (s.equals("电力用户名称") || s.equals("月份") || s.equals("状态") || s.equals("商品名称")
                                || s.equals("是否分时") || s.equals("时段")) {
                            listMap.put(s, key);
                        } else if (s.equals("基础价格套餐/基准套餐")) {
                            listMap.put("价格值名称", key);
                            listMap.put("价格值", key + 1);
                            listMap.put("价差值", key + 2);
                            break;
                        }
                    }
                }

                List<VppRetailContractsManageVO> dataList = new ArrayList<>();
                for (int i = 3; i < excelList.size(); i++) {
                    VppRetailContractsManageVO vppRetailContractsManageVO = new VppRetailContractsManageVO();
                    LinkedHashMap<Integer, String> map = excelList.get(i);

                    Integer status = listMap.get("状态");
                    if (!map.get(status).equals("已解约")) {
                        Integer isTimeSharing = listMap.get("是否分时");
                        if (map.get(isTimeSharing).equals("是")) {
                            Integer nameNumber = listMap.get("电力用户名称");
                            String name = map.get(nameNumber);
                            if (ObjectUtil.isNotEmpty(nameMap.get(name))) {
                                vppRetailContractsManageVO.setCycle(1);
                                vppRetailContractsManageVO.setRetailContractsId(records.get(0).getId());
                                vppRetailContractsManageVO.setSigningMonth(DateUtil.parseDate(runMonth + "-01"));
                                vppRetailContractsManageVO.setType(2);
                                vppRetailContractsManageVO.setName(name);
                                vppRetailContractsManageVO.setUserId(nameMap.get(name).get(0).getId());
                                vppRetailContractsManageVO.setPriceName(map.get(listMap.get("价格值名称")));
                                if (!map.get(listMap.get("价差值")).equals("-")) {
                                    vppRetailContractsManageVO.setPriceDifference(new BigDecimal(map.get(listMap.get("价差值"))));
                                }
                                if (!map.get(listMap.get("价格值")).equals("-")) {
                                    vppRetailContractsManageVO.setVal(new BigDecimal(map.get(listMap.get("价格值"))));
                                }
                                vppRetailContractsManageVO.setTradeName(map.get(listMap.get("商品名称")));
                                vppRetailContractsManageVO.setTenantId(Long.valueOf(tenantId));
                                Integer periodNumber = listMap.get("时段");
                                String period = map.get(periodNumber);
                                String twentyFourTime = VppConstant.TWENTY_FOUR_TIMES[Integer.parseInt(period) - 1];
                                vppRetailContractsManageVO.setTimeFrame(twentyFourTime);
                                dataList.add(vppRetailContractsManageVO);
                            }
                        }
                    }
                }
                Map<String, List<VppRetailContractsManageVO>> userList = dataList.stream().collect(Collectors.groupingBy(VppRetailContractsManageVO::getName));
                if (nameMap.size() != userList.size()) {
                    return DataResult.failed("导入失败，零售合同用户名称不符，请修改后重试！");
                }
                //上传文件
                DataResult<VppFileUploadVO> fileUploadVODataResult = vppFileApi.uploadFile(file);
                String name = fileUploadVODataResult.getData().getName();
                String url = fileUploadVODataResult.getData().getUrl();

                QueryImportRecordDTO queryImportRecordDTO = new QueryImportRecordDTO();
                queryImportRecordDTO.setList(dataList);
                queryImportRecordDTO.setName(name);
                queryImportRecordDTO.setUrl(url);
                queryImportRecordDTO.setRetailContractsId(records.get(0).getId());
                queryImportRecordDTO.setTenantId(Long.valueOf(tenantId));


                return vppRetailContractsApi.queryImportRecord(queryImportRecordDTO);
            } catch (
                    Exception e) {
                log.error("导入模板不正确" + e);
                return DataResult.failed("导入模板不正确");
            }
        } else {
            return DataResult.failed("虚拟电厂不存在");
        }

    }

    @Override
    public DataResult<Void> updateStatus(DataMaintenanceUpdateDTO param) {
        return dataMaintenanceApi.updateStatus(param);
    }

    @Resource
    private VppElectricQuantityService vppElectricQuantityService;

    @Override
    public DataResult<Void> importElectric(MultipartFile file) throws IOException {
        vppElectricQuantityService.importElectricNew(file, null);
        return DataResult.success();
    }

}
