package com.fx.green.electricity.shanxi.server.service.data.impl;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.api.data.DataMaintenanceApi;
import com.fx.green.electricity.shanxi.api.api.data.VppRetailContractsApi;
import com.fx.green.electricity.shanxi.api.api.file.VppFileApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import com.fx.green.electricity.shanxi.server.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.server.service.data.VppElectricQuantityService;
import com.fx.green.electricity.shanxi.server.service.user.VppLoadUserService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.List;

/**
 * 数据维护 Service 实现类
 */
@Service
@Slf4j
public class DataMaintenanceServiceImpl implements DataMaintenanceService {

    @Resource
    private DataMaintenanceApi dataMaintenanceApi;

    @Resource
    private VppRetailContractsApi vppRetailContractsApi;

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Resource
    private VppFileApi vppFileApi;


    @Override
    public DataResult<List<DataMaintenanceVO>> queryList(DataMaintenanceDTO param) {
        return dataMaintenanceApi.queryList(param);
    }

    @Override
    public DataResult<List<ExpireUserVO>> getExpireUserList(DataMaintenanceDTO param) {
        return dataMaintenanceApi.getExpireUserList(param);
    }

    @Override
    public DataResult<Integer> geUserCount(DataMaintenanceDTO param) {
        return dataMaintenanceApi.geUserCount(param);
    }

    @Override
    public DataResult<List<ElectricityDetailVO>> getElectricDetail(DataMaintenanceQueryDTO param) {
        return dataMaintenanceApi.getElectricDetail(param);
    }

    @Override
    public DataResult<Void> delElectric(DataMaintenanceQueryDTO param) {
        return dataMaintenanceApi.delElectric(param);
    }

    @Override
    public DataResult<Void> importRetailContract(MultipartFile file, String runMonth) {
        return vppRetailContractsApi.importRetailContract(file, runMonth);
    }

    @Override
    public DataResult<Void> updateStatus(DataMaintenanceUpdateDTO param) {
        return dataMaintenanceApi.updateStatus(param);
    }

    @Resource
    private VppElectricQuantityService vppElectricQuantityService;

    @Override
    public DataResult<Void> importElectric(MultipartFile file) throws IOException {
        vppElectricQuantityService.importElectricNew(file, null);
        return DataResult.success();
    }

}
