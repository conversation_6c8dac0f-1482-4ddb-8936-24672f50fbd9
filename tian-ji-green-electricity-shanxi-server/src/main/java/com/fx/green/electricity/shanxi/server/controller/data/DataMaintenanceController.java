package com.fx.green.electricity.shanxi.server.controller.data;


import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import com.fx.green.electricity.shanxi.server.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.server.service.data.VppElectricQuantityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 数据维护 Controller
 */
@Api(tags = "数据维护 - 数据维护")
@RestController
@RequestMapping("/dataMaintenance")
public class DataMaintenanceController {

    @Resource
    private DataMaintenanceService dataMaintenanceService;

    @Resource
    private VppElectricQuantityService vppElectricQuantityService;

    @ApiOperation("数据维护列表展示")
    @PostMapping("/queryList")
    public DataResult<List<DataMaintenanceVO>> queryList(@RequestBody @Valid DataMaintenanceDTO param) {
        return dataMaintenanceService.queryList(param);
    }

    @CommonNoRepeat(interval = 3000)
    @ApiOperation("零售合同 - 导入")
    @PostMapping("/importRetailContract")
    public DataResult<Void> importRetailContract(@RequestPart("file") MultipartFile file,
                                                 @RequestParam("runMonth") @ApiParam("运行月份") String runMonth) {
        return dataMaintenanceService.importRetailContract(file, runMonth);
    }

    @CommonNoRepeat(interval = 3000)
    @ApiOperation("日前申报 - 导入")
    @PostMapping("/importRecord")
    public DataResult<ImportExcelVO> importRecord(@RequestPart("file") MultipartFile file,
                                                  @RequestParam("time") @ApiParam("运行时间") String time) {
        return vppElectricQuantityService.importRecord(file, time);
    }

    @ApiOperation("申报数据 - 下载")
    @PostMapping("/downloadRecord")
    public DataResult<Void> downloadRecord(@RequestBody CommonDTO.DateDTO param) {
        return vppElectricQuantityService.downloadRecord(param);
    }

    @ApiOperation("申报数据 - 删除某一天的数据")
    @PostMapping("/deleteRecordData")
    public DataResult<Void> deleteRecordData(@RequestBody DeleteRecordDTO deleteRecordDTO) {
        return vppElectricQuantityService.deleteRecordData(deleteRecordDTO);
    }

    @ApiOperation("获取即将失效的用户")
    @PostMapping("getExpireUserList")
    public DataResult<List<ExpireUserVO>> getExpireUserList(@RequestBody @Valid DataMaintenanceDTO param) {
        return dataMaintenanceService.getExpireUserList(param);
    }

    @ApiOperation("获取生效的用户的数量")
    @PostMapping("geUserCount")
    public DataResult<Integer> geUserCount(@RequestBody @Valid DataMaintenanceDTO param) {
        return dataMaintenanceService.geUserCount(param);
    }

    @CommonNoRepeat(interval = 3000)
    @ApiOperation("实际用电量 - 导入")
    @PostMapping("/importElectric")
    public DataResult<Void> importElectric(@RequestPart("file") MultipartFile file) throws IOException {
        return dataMaintenanceService.importElectric(file);
    }

    @ApiOperation("实际用电量 - 详情")
    @PostMapping("/getElectricDetail")
    public DataResult<List<ElectricityDetailVO>> getElectricDetail(@RequestBody @Valid DataMaintenanceQueryDTO param) {
        return dataMaintenanceService.getElectricDetail(param);
    }

    @ApiOperation("实际用电量 - 删除")
    @PostMapping("/delElectric")
    public DataResult<Void> delElectric(@RequestBody @Valid DataMaintenanceQueryDTO param) {
        return dataMaintenanceService.delElectric(param);
    }

}
