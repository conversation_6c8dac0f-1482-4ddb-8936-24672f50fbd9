package com.fx.green.electricity.shanxi.server.service.unit.impl;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.server.service.unit.GeTenantService;
import com.fx.green.electricity.shanxi.server.utils.OperationApiUtil;
import com.fx.operation.api.vo.TenantVO;
import com.fx.green.electricity.shanxi.api.api.GeUnitApi;
import com.fx.green.electricity.shanxi.api.pojo.dto.unit.GeTenantDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.unit.GeTenantVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 维护绿电租户信息
 */
@Service
public class GeTenantServiceImpl implements GeTenantService {

    @Autowired
    private OperationApiUtil operationApiUtil;
    @Autowired
    private GeUnitApi geUnitApi;

    /**
     * 获取绿电直连租户和机组
     *
     * @return
     */
    @Override
    public List<TenantVO> getGreenTenantUnit() {
        return operationApiUtil.getTenantUnitList();
    }

    /**
     * 新增绿电租户
     *
     * @param param
     */
    @Override
    public DataResult<Void> insertTenant(GeTenantDTO param) {
        return geUnitApi.insertTenant(param);
    }

    /**
     * 删除绿电租户
     *
     * @param param
     */
    @Override
    public DataResult<Void> deleteTenant(IdDTO param) {
        return geUnitApi.deleteTenant(param);
    }

    /**
     * 绿电租户分页
     *
     * @param param
     * @return
     */
    @Override
    public DataResult<List<GeTenantVO>> tenantList(GeTenantDTO param) {
        return geUnitApi.tenantList(param);
    }

    /**
     * 获取所有租户和机组对应信息
     *
     * @return
     */
    @Override
    public DataResult<List<GeTenantVO>> getTenantUnitDetail() {
        return geUnitApi.getTenantUnitDetail();
    }
}
