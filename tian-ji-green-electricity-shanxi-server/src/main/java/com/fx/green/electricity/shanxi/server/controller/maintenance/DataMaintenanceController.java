package com.fx.green.electricity.shanxi.server.controller.maintenance;


import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import com.fx.green.electricity.shanxi.server.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.server.service.data.VppElectricQuantityService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 数据维护
 */
@Api(tags = "数据维护Controller")
@RestController
@RequestMapping("dataMaintenance")
public class DataMaintenanceController {

    @Resource
    private DataMaintenanceService dataMaintenanceService;

    @Resource
    private VppElectricQuantityService vppElectricQuantityService;

    @ApiOperation("数据维护列表展示")
    @PostMapping("queryList")
    public DataResult<List<DataMaintenanceVO>> queryList(@RequestBody @Valid DataMaintenanceDTO param) {
        return dataMaintenanceService.queryList(param);
    }

    @ApiOperation("导入零售合同")
    @CommonNoRepeat(interval = 3000)
    @PostMapping("importRetailContract")
    public DataResult<Void> importRetailContract(@RequestPart("file") MultipartFile file, @ApiParam("运行月份") String runMonth, HttpServletRequest request) {
        return dataMaintenanceService.importRetailContract(file, runMonth, request);
    }


    @ApiOperation("日前申报数据导入")
    @CommonNoRepeat(interval = 3000)
    @PostMapping("importRecord")
    public DataResult<ImportExcelVO> importRecord(@RequestPart("file") MultipartFile file,
                                                  @RequestParam("time") @ApiParam("运行时间") String time) {
        return vppElectricQuantityService.importRecord(file, time);
    }


    @ApiOperation("下载申报数据")
    @PostMapping("downloadRecord")
    public DataResult<Void> downloadRecord(@RequestBody CommonDTO.DateDTO param) {
        return vppElectricQuantityService.downloadRecord(param);
    }

    @ApiOperation("删除某一天的申报数据")
    @PostMapping("deleteRecordData")
    public DataResult<Void> deleteRecordData(@RequestBody DeleteRecordDTO deleteRecordDTO) {
        return vppElectricQuantityService.deleteRecordData(deleteRecordDTO);
    }

    @ApiOperation("获取即将失效的用户")
    @PostMapping("getExpireUserList")
    public DataResult<List<ExpireUserVO>> getExpireUserList(@RequestBody @Valid DataMaintenanceDTO param) {
        return dataMaintenanceService.getExpireUserList(param);
    }

    @ApiOperation("获取生效的用户的数量")
    @PostMapping("geUserCount")
    public DataResult<Integer> geUserCount(@RequestBody @Valid DataMaintenanceDTO param) {
        return dataMaintenanceService.geUserCount(param);
    }

    //实际用电量

    @ApiOperation("导入实际用电量")
    @CommonNoRepeat(interval = 3000)
    @PostMapping("importElectric")
    public DataResult<Void> importElectric(@RequestPart("file") MultipartFile file) throws IOException {
        return dataMaintenanceService.importElectric(file);
    }

    @ApiOperation("实际用电量详情")
    @PostMapping("getElectricDetail")
    public DataResult<List<ElectricityDetailVO>> getElectricDetail(@RequestBody @Valid DataMaintenanceQueryDTO param) {
        return dataMaintenanceService.getElectricDetail(param);
    }


    @ApiOperation("删除实际用电量")
    @PostMapping("delElectric")
    public DataResult<Void> delElectric(@RequestBody @Valid DataMaintenanceQueryDTO param) {
        return dataMaintenanceService.delElectric(param);
    }


}
