package com.fx.green.electricity.shanxi.server.service.data;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDataDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDayRecordVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO;

import java.util.List;

/**
 * 文件导入记录
 */
public interface GeFileRecordService {

    /**
     * 文件维护记录
     *
     * @param param
     * @return
     */
    DataResult<List<GeDayRecordVO>> getFileRecordByDay(GeFileRecordDTO param);

    /**
     * 文件列表状态
     *
     * @param param
     * @return
     */
    DataResult<List<GeFileRecordVO>> fileStatus(GeFileRecordDTO param);

    /**
     * 获取当日下数据
     *
     * @param param
     * @return
     */
    DataResult<List<GeDataDetailVO>> getInfoDataValue(GeFileRecordDTO param);
}
