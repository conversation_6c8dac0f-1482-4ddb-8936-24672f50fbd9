package com.fx.green.electricity.shanxi.server.controller.maintenance;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.GeFileRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDataDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeDayRecordVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO;
import com.fx.green.electricity.shanxi.server.service.data.GeFileRecordService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 文件记录数据
 */
@RestController
@Api(tags = "文件记录数据")
@RequestMapping("geFileRecord")
public class GeFileRecordController {

    @Autowired
    private GeFileRecordService geFileRecordService;

    /**
     * 文件维护记录
     *
     * @param param
     * @return
     */
    @ApiOperation("文件维护记录")
    @PostMapping("getFileRecordByDay")
    public DataResult<List<GeDayRecordVO>> getFileRecordByDay(@RequestBody GeFileRecordDTO param) {
        return geFileRecordService.getFileRecordByDay(param);
    }

    /**
     * 文件列表状态
     *
     * @param param
     * @return
     */
    @ApiOperation("文件列表状态")
    @PostMapping("fileStatus")
    public DataResult<List<GeFileRecordVO>> fileStatus(@RequestBody GeFileRecordDTO param) {
        return geFileRecordService.fileStatus(param);
    }

    /**
     * 获取当日下数据
     *
     * @param param
     * @return
     */
    @ApiOperation("获取当日下数据")
    @PostMapping("getInfoDataValue")
    public DataResult<List<GeDataDetailVO>> getInfoDataValue(@RequestBody GeFileRecordDTO param) {
        return geFileRecordService.getInfoDataValue(param);
    }
}
