<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.fx</groupId>
        <artifactId>tian-ji-green-electricity-shanxi</artifactId>
        <version>1.0.0.RELEASE</version>
    </parent>

    <artifactId>tian-ji-green-electricity-shanxi-api</artifactId>
    <version>${parent.version}</version>
    <name>>${project.artifactId}</name>
    <description>山西绿电直连平台api</description>

    <dependencies>
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
        </dependency>

        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-common-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-mqtt-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.fx</groupId>
            <artifactId>tian-ji-public-data-shanxi-api</artifactId>
            <classifier>jdk1.8</classifier>
        </dependency>
    </dependencies>

    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <parameters>true</parameters>
                    <encoding>${project.build.sourceEncoding}</encoding>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>