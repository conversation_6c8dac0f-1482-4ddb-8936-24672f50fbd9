package com.fx.green.electricity.shanxi.api.api;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.excel.vo.ImportExcelDetailVO;
import com.fx.common.excel.vo.ImportExcelVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.VppElectricQuantityImportListDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.GetUpdateTimeDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 **/
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppApi")
public interface VppApi {

    /**
     * 电量数据导入
     *
     * @param param 电量数据实体
     * @return 结果
     */
    @PostMapping("vppElectricQuantity/importElectric")
    DataResult<ImportExcelDetailVO> vppElectricQuantityImportElectric(@RequestBody VppElectricQuantityDTO param);

    /**
     * 电量数据导入
     *
     * @param param 电量数据实体
     * @return 结果
     */
    @PostMapping("vppElectricQuantity/importElectricNew")
    DataResult<ImportExcelDetailVO> vppElectricQuantityImportElectricNew(@RequestBody VppElectricQuantityImportListDTO param);

    /**
     * 查询实际用电量导入记录
     *
     * @param param
     * @return
     */
    @PostMapping("vppElectricQuantity/queryImportRecord")
    DataResult<FxPage<ImportExcelDetailPageListVO>> queryImportRecord(@RequestBody VppElectricQuantityDTO.QueryDTO param);

    /**
     * 日前申报数据导入
     *
     * @param vppElectricQuantityDTO
     * @return
     */
    @PostMapping("vppElectricQuantity/importRecord")
    DataResult<ImportExcelVO> importRecord(@RequestBody VppElectricQuantityDTO vppElectricQuantityDTO);


    @PostMapping("vppElectricActual/getUpdateTime")
    DataResult<String> getUpdateTime(@RequestBody GetUpdateTimeDTO param);

    /**
     * 删除日前申报数据
     *
     * @param deleteRecordDTO
     * @return
     */
    @PostMapping("vppElectricQuantity/deleteRecordData")
    DataResult<Void> deleteRecordData(@RequestBody DeleteRecordDTO deleteRecordDTO);

    /**
     * 下载日前申报数据
     *
     * @param param
     * @return
     */
    @PostMapping("vppElectricQuantity/downloadRecord")
    DataResult<List<SeElectricDeclareVO>> downloadRecord(@RequestBody CommonDTO.DateDTO param);
}
