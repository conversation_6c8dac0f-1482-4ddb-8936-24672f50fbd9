package com.fx.green.electricity.shanxi.api.api;

import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.predicted.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.QueryPredictedElectricityContrastVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.QueryPredictedElectricityVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.VppPredictedExcludeDateVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.predicted.VppPredictedReferenceDateVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR>
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppPredictedElectricityApi")
public interface VppPredictedElectricityApi {


    @PostMapping("vppPredictedElectricity/queryTreeListNew")
    DataResult<VppLoadUserVO.TreeVO> queryTreeListNew(@RequestBody QueryTreeListDTO param);

    @PostMapping("vppPredictedElectricity/queryPredictedElectricity")
    DataResult<QueryPredictedElectricityVO> queryPredictedElectricity(@RequestBody QueryPredictedElectricityDTO param);

    @PostMapping("vppPredictedElectricity/getReferenceDate")
    DataResult<VppPredictedReferenceDateVO> getReferenceDate(@RequestBody QueryDateOneDTO param);

    @PostMapping("vppPredictedElectricity/saveOrUpdateElectricity")
    DataResult<Void> saveOrUpdateElectricity(@RequestBody UpdatePredictedElectricityDTO param);

    @PostMapping("vppPredictedElectricity/confirmPrediction")
    DataResult<Void> confirmPrediction(@RequestBody ConfirmPredictionDTO param);

    @PostMapping("vppPredictedElectricity/resetPrediction")
    DataResult<Void> resetPrediction(@RequestBody ResetPredictedElectricityDTO param);

    @PostMapping("vppPredictedElectricity/getPredictedContrastData")
    DataResult<QueryPredictedElectricityContrastVO> getPredictedContrastData(@RequestBody QueryPredictedElectricityDTO param);


    @PostMapping("vppPredictedElectricity/addExcludeDay")
    DataResult<Void> addExcludeDay(@RequestBody VppPredictedExcludeDayDTO param);

    @PostMapping("vppPredictedElectricity/getExcludeDay")
    DataResult<VppPredictedExcludeDateVO> getExcludeDay(@RequestBody QueryDateOneDTO param);
}
