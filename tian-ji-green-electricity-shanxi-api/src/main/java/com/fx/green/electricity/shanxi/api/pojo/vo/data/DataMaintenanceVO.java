package com.fx.green.electricity.shanxi.api.pojo.vo.data;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.vo.BaseVO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.util.Date;
/**
 * 数据维护列表VO
 */
@Data
@ApiModel("数据维护列表")
@EqualsAndHashCode(callSuper = true)
public class DataMaintenanceVO extends BaseVO {

    @ApiModelProperty("虚拟电厂Id")
    private Long tenantId;

    @JsonFormat(pattern = "yyyy-MM-dd")
    @ApiModelProperty("日期（天）")
    private Date dateDay;

    @JsonFormat(pattern = "yyyy-MM")
    @ApiModelProperty("日期（月）")
    private Date dateMonth;

    @ApiModelProperty("中长期维护状态(1.已维护，2.未维护，3.维护不全)")
    private int mediumLongTermStatus;

    @ApiModelProperty("日前申报维护状态(1.已维护，2.未维护)")
    private int declareStatus;

    @ApiModelProperty("实际用电量维护状态(1.已维护，2.未维护，3.维护不全)")
    private int realElctricityStatus;

    @ApiModelProperty("出清结果维护状态(1.已维护，2.未维护)")
    private int clearResultStatus;

    @ApiModelProperty("占比")
    private BigDecimal proportion;

    @ApiModelProperty("出清结果数据来源(1.省级智慧能源平台 2.导入 3.灵狐推送)")
    private Integer clearDataSources;

    @ApiModelProperty("日前申报数据来源(1.接口同步 2.导入 3.灵狐推送)")
    private Integer declareSources;

    @ApiModelProperty("实际用电量数据来源(1.导入，2.灵狐推送)")
    private Integer realElectricitySources;

    @ApiModelProperty("中长期数据来源(1.导入，2.灵狐推送)")
    private Integer mediumLongTermSources;

}
