package com.fx.green.electricity.shanxi.api.api.data;


import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * 数据维护 Api 接口
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "DataMaintenanceApi")
public interface DataMaintenanceApi {

    /**
     * 查询数据维护列表（月）
     * 
     * @param param 数据维护DTO
     * @return 数据维护列表
     */
    @PostMapping("/dataMaintenance/queryList")
    DataResult<List<DataMaintenanceVO>> queryList(@RequestBody DataMaintenanceDTO param);

    /**
     * 获取即将失效用户
     * 
     * @param param 数据维护DTO
     * @return 即将失效用户列表
     */
    @PostMapping("/dataMaintenance/getExpireUserList")
    DataResult<List<ExpireUserVO>> getExpireUserList(DataMaintenanceDTO param);


    /**
     * 查询实际用电量详情
     *
     * @param param 数据维护DTO
     * @return 实际用电量详情列表
     */
    @PostMapping("/dataMaintenance/getElectricDetail")
    DataResult<List<ElectricityDetailVO>> getElectricDetail(DataMaintenanceQueryDTO param);

    /**
     * 删除实际用电量
     *
     * @param param 数据维护DTO
     * @return 结果
     */
    @PostMapping("/dataMaintenance/delElectric")
    DataResult<Void> delElectric(DataMaintenanceQueryDTO param);


    /**
     * 修改数据维护状态
     *
     * @param param 数据维护DTO
     * @return 结果
     */
    @PostMapping("/dataMaintenance/updateStatus")
    DataResult<Void> updateStatus(DataMaintenanceUpdateDTO param);


    /**
     * 获取生效的用户的数量
     * @param param 数据维护DTO
     * @return 生效的用户的数量
     */
    @PostMapping("/dataMaintenance/geUserCount")
    DataResult<Integer> geUserCount(DataMaintenanceDTO param);
}
