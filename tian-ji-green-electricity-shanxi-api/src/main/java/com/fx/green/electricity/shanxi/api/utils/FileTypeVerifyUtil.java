package com.fx.green.electricity.shanxi.api.utils;

import cn.hutool.core.text.CharSequenceUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.tika.Tika;
import org.apache.tika.metadata.Metadata;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;

/**
 * 文件类型校验
 *
 * <AUTHOR>
 **/
@Slf4j
public class FileTypeVerifyUtil {

    private static final Tika tika = new Tika();

    /**
     * 校验excel文件类型是否正确（基于文件名）
     */
    public static boolean checkExcelFile(String fileName) {
        if (CharSequenceUtil.isBlank(fileName)) {
            return false;
        }
        return fileName.toLowerCase().endsWith("xls") || fileName.toLowerCase().endsWith("xlsx");
    }

    /**
     * 校验excel文件类型是否正确（基于文件内容和文件名）
     * 使用 Apache Tika 进行更准确的文件类型检测
     */
    public static boolean checkExcelFile(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return false;
        }

        // 首先检查文件名
        String fileName = file.getOriginalFilename();
        if (!checkExcelFile(fileName)) {
            return false;
        }

        // 然后使用 Tika 检测文件内容
        try (InputStream inputStream = file.getInputStream()) {
            Metadata metadata = new Metadata();
            if (CharSequenceUtil.isNotBlank(fileName)) {
                metadata.set(Metadata.RESOURCE_NAME_KEY, fileName);
            }

            String mimeType = tika.detect(inputStream, metadata);
            log.debug("检测到的 MIME 类型: {}, 文件名: {}", mimeType, fileName);

            // 检查是否为 Excel 文件的 MIME 类型
            return isExcelMimeType(mimeType);
        } catch (IOException e) {
            log.error("检测 Excel 文件类型时发生异常: {}", e.getMessage(), e);
            // 如果检测失败，回退到文件名检查
            return checkExcelFile(fileName);
        }
    }

    /**
     * 判断 MIME 类型是否为 Excel 文件
     */
    private static boolean isExcelMimeType(String mimeType) {
        if (CharSequenceUtil.isBlank(mimeType)) {
            return false;
        }

        return "application/vnd.ms-excel".equals(mimeType) ||
               "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet".equals(mimeType) ||
               // xlsx 文件可能被检测为 zip，这是正常的
               ("application/zip".equals(mimeType));
    }
}
