package com.fx.green.electricity.shanxi.api.pojo.dto.retail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.common.dto.BaseDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 *
 **/
@Data
@ApiModel("零售合同表")
public class QueryVppRetailContractsDTO extends BaseDTO {

    private static final long serialVersionUID = 1550146671611595797L;

    @ApiModelProperty("开始时间")
    @JsonFormat(pattern = "yyyy-MM")
    private Date startDate;

    @ApiModelProperty("结束时间")
    @JsonFormat(pattern = "yyyy-MM")
    private Date endDate;

    @ApiModelProperty("导入状态 0未导入 1导入")
    private Integer status;

    @ApiModelProperty("公司名称")
    private String name;

    @ApiModelProperty("商品名称")
    private String tradeName;

    @ApiModelProperty("红利系数")
    private BigDecimal dividendSharCoefficient;

    @ApiModelProperty("红利系数状态 1未录入 2录入 ")
    private Integer type;

}