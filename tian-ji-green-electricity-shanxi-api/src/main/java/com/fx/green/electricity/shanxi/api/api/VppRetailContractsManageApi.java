package com.fx.green.electricity.shanxi.api.api;

import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.RetailContractsManageDetailDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.UpdateRetailContractsManageDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageListVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

/**
 * <AUTHOR>
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppRetailContractsManageApi")
public interface VppRetailContractsManageApi {

    /**
     * 获取零售列表
     *
     * @param param
     * @return
     */
    @PostMapping("vppRetailContractsManage/getList")
    DataResult<FxPage<VppRetailContractsManageListVO>> getList(@RequestBody QueryVppRetailContractsDTO param);

    /**
     * 零售合同详情
     *
     * @param param
     * @return
     */
    @PostMapping("vppRetailContractsManage/retailContractsManageDetail")
    DataResult<VppRetailContractsManageDetailVO> retailContractsManageDetail(@RequestBody RetailContractsManageDetailDTO param);

    /**
     * 修改用户的红利系数
     *
     * @param param
     * @return
     */
    @PostMapping("vppRetailContractsManage/updateDividendCoefficient")
    DataResult<Void> updateDividendCoefficient(@RequestBody UpdateRetailContractsManageDTO param);


    /**
     * 导出列表
     *
     * @param param
     * @return
     */
    @PostMapping("vppRetailContractsManage/exportManageList")
    DataResult<List<VppRetailContractsManageListVO>> exportManageList(@RequestBody QueryVppRetailContractsDTO param);
}
