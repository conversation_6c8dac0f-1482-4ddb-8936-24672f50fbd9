package com.fx.green.electricity.shanxi.api.api.production;

import com.fx.common.constant.DataResult;
import com.fx.common.dto.IdDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.production.VppUserProductionDataDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.production.VppUserProductionDataVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;

/**
 * 用户生产数据 Api 接口
 *
 * <AUTHOR>
 */
@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppUserProductionDataApi")
public interface VppUserProductionDataApi {

    /**
     * 根据用户id查询用户生产数据
     *
     * @param param 负荷用户id
     * @return 成功/失败
     */
    @PostMapping("VppUserProductionData/findByUserId")
    DataResult<VppUserProductionDataVO> getProductionDataByUserId(IdDTO param);

    /**
     *保存用户生产数据
     * @param param 用户生产数据DTO
     * @return 成功/失败
     */
    @PostMapping("VppUserProductionData/saveOrUpdate")
    DataResult<Void> saveOrUpdate(VppUserProductionDataDTO param);
}
