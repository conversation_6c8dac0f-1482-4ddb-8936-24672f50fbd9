package com.fx.green.electricity.shanxi.api.api;


import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsPriceQueryVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsPriceVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

import java.util.List;

@FeignClient(value = "tianJiGreenElectricityShanxiService", contextId = "VppRetailContractsPriceApi")
public interface VppRetailContractsPriceApi {

    /**
     * 获取数据列表
     *
     * @param param
     * @return
     */
    @PostMapping("vppRetailContractsPrice/dataList")
    DataResult<List<VppRetailContractsPriceVO>> getPriceList(@RequestBody VppRetailContractsPriceQueryVO param);
}
