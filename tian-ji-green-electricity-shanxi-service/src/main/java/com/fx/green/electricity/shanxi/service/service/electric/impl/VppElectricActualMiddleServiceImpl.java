package com.fx.green.electricity.shanxi.service.service.electric.impl;


import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualMiddle;
import com.fx.green.electricity.shanxi.service.mapper.electric.VppElectricActualMiddleMapper;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualMiddleService;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Service
public class VppElectricActualMiddleServiceImpl extends ServiceImpl<VppElectricActualMiddleMapper, VppElectricActualMiddle> implements VppElectricActualMiddleService {

    @Override
    public List<VppElectricActualMiddle> queryByUserIdList(List<Long> userIdList, Date startTime, Date endTime, Long tenantId) {
        if (CollUtil.isEmpty(userIdList)) {
            return new ArrayList<>();
        }
        LambdaQueryWrapper<VppElectricActualMiddle> electricMiddleLqw = new LambdaQueryWrapper<>();
        electricMiddleLqw.ge(VppElectricActualMiddle::getDateDay, startTime);
        electricMiddleLqw.le(VppElectricActualMiddle::getDateDay, endTime);
        electricMiddleLqw.in(VppElectricActualMiddle::getUserId, userIdList);
        electricMiddleLqw.eq(VppElectricActualMiddle::getTenantId, tenantId);
        return this.list(electricMiddleLqw);
    }

    @Override
    public void removeByParam(Date dateDay, Long tenantId) {
        baseMapper.removeByParam(dateDay, tenantId);
    }
}
