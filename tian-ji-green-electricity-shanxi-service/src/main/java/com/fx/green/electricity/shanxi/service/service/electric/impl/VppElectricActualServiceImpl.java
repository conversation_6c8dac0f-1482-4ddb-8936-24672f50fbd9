package com.fx.green.electricity.shanxi.service.service.electric.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.IdWorker;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.TimeConstant;
import com.fx.common.enums.BooleanEnum;
import com.fx.common.excel.vo.ImportExcelDetailVO;
import com.fx.common.util.MyDateUtil;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.*;
import com.fx.green.electricity.shanxi.service.constant.PartitionTableConstant;
import com.fx.green.electricity.shanxi.service.constant.VppConstant;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActual;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricQuantity;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.enums.VirtualPowerPlantServiceCodeEnum;
import com.fx.green.electricity.shanxi.service.mapper.electric.VppElectricActualMapper;
import com.fx.green.electricity.shanxi.service.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualConvergeService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualService;
import com.fx.green.electricity.shanxi.service.service.other.VppPartitionTableService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import com.fx.green.electricity.shanxi.service.utils.BeanCopyUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 **/
@Service
public class VppElectricActualServiceImpl extends ServiceImpl<VppElectricActualMapper, VppElectricActual> implements VppElectricActualService {

    @Autowired
    private VppLoadUserService vppLoadUserService;

    @Autowired
    private VppPartitionTableService vppPartitionTableService;
    @Autowired
    private DataMaintenanceService dataMaintenanceService;


    @Autowired
    private VppElectricActualConvergeService vppElectricActualConvergeService;

    private static final Integer INSERT_SIZE = 1000;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ImportExcelDetailVO importVppElectricActualList(List<VppElectricActualDTO> vppElectricActualDTOList, VppElectricQuantity param) {
        ImportExcelDetailVO importExcelDetail = new ImportExcelDetailVO();
        importExcelDetail.setInfoList(new ArrayList<>());

        int success = 0;
        int failed = 0;

        List<VppUser> userList = vppLoadUserService.getListByTenantId(param.getTenantId(), "");
        Map<String, VppUser> userNameMap = userList.stream().collect(Collectors.toMap(VppUser::getName, u -> u));
        List<VppElectricActual> electricActualList = new ArrayList<>();

        List<BigDecimal> electricList = new ArrayList<>();
        List<String> userNameList = new ArrayList<>();
        for (VppElectricActualDTO vppElectricActualDTO : vppElectricActualDTOList) {
            String userName = vppElectricActualDTO.getName();
            if (!userNameMap.containsKey(userName)) {
                continue;
            }
            VppUser vppUser = userNameMap.get(userName);
            vppElectricActualDTO.setUserCode(vppUser.getUserCode());
            vppElectricActualDTO.setUserId(vppUser.getId());

            VppElectricActual vppElectricActual = new VppElectricActual();
            BeanUtils.copyProperties(vppElectricActualDTO, vppElectricActual);
            vppElectricActual.setId(IdWorker.getId());
            vppElectricActual.setIsDelete(0);
            electricActualList.add(vppElectricActual);
            electricList.add(vppElectricActual.getElectricity());
            // 所有导入的名称整理
            if (!userNameList.contains(userName)) {
                userNameList.add(userName);
            }
        }
        success += electricActualList.size();

        if (electricList.isEmpty()) {
            importExcelDetail.getInfoList().add("异常原因：" +
                    VirtualPowerPlantServiceCodeEnum.IMPORT_NO_BIND_USER.getMessage());
            failed += 1;
        }

        if (electricList.size() % VppConstant.NINETY_SIX_NUMBER != 0) {
            importExcelDetail.getInfoList().add("异常原因：" +
                    VirtualPowerPlantServiceCodeEnum.IMPORT_QUANTITY_ERROR.getMessage());
            failed += 1;
        }
        if (failed > 0) {
            // 如果有异常的 手动回滚
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        } else {
            //分表需要tenantId
            vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL, param.getRunningDate());

            // 判断是否已经存在，已经存在进行修改操作，如果没有的话在直接新增
            LambdaQueryWrapper<VppElectricActual> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VppElectricActual::getElectricId, param.getId());
            List<VppElectricActual> oldElectricActualList = this.list(queryWrapper);

            // 获取所有的旧数据
            Map<String, List<VppElectricActual>> oldElectricActualMap = oldElectricActualList.stream()
                    .collect(Collectors.groupingBy(VppElectricActual::getName));

            // 获取所有的新数据
            Map<String, List<VppElectricActual>> electricActualMap = electricActualList.stream()
                    .collect(Collectors.groupingBy(VppElectricActual::getName));

            List<VppElectricActual> insertList = new ArrayList<>();
            List<VppElectricActual> updateList = new ArrayList<>();
            electricActualMap.forEach((name, dataList) -> {
                if (oldElectricActualMap.containsKey(name)) {
                    // 旧数据进行更新
                    List<VppElectricActual> electricActualDataList = oldElectricActualMap.get(name);
                    Map<String, List<VppElectricActual>> electricActualDataMap = electricActualDataList.stream()
                            .collect(Collectors.groupingBy(VppElectricActual::getTimeFrame));

                    // 遍历新数据 将旧数据的ID赋过去
                    for (VppElectricActual vppElectricActual : dataList) {
                        String timeFrame = vppElectricActual.getTimeFrame();
                        // 判断之前是否有上传的
                        List<VppElectricActual> list = electricActualDataMap.get(timeFrame);
                        VppElectricActual oldData = list.get(0);
                        vppElectricActual.setId(oldData.getId());
                    }
                    updateList.addAll(dataList);

                } else {
                    insertList.addAll(dataList);
                }
            });

            // 批量新增数据
            this.insertBatch(insertList);
            // 批量修改数据
            this.updateBatchById(updateList);
        }

        importExcelDetail.setSuccessNum(success);
        importExcelDetail.setFailedNum(failed);

        if (failed > 0) {
            importExcelDetail.setMessage("部分数据内容异常");
            importExcelDetail.setStatus(false);
        } else {
            importExcelDetail.setStatus(true);

            // 相关分区表创建
            vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_24, param.getRunningDate());
            vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_CONVERGE, param.getRunningDate());
            // 直接创建连后三个月的 如果日多了27号 将下下个月的表也创建出来
            // 预测数据是要生成这个月以及下个月的表
            int months = DateUtil.dayOfMonth(param.getRunningDate()) > 27 ? 3 : 2;
            for (int i = 0; i < months; i++) {
                vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_MIDDLE, DateUtil.offsetMonth(param.getRunningDate(), i));
            }

            // 存储过程调用
            baseMapper.importElectricCalculate(param.getTenantId(), param.getRunningDate(), param.getId());
        }

        return importExcelDetail;
    }


    @Override
    public void deleteByElectricId(Long electricId) {
        baseMapper.deleteByElectricId(electricId);
    }


    @Override
    public void insertBatch(List<VppElectricActual> eleList) {
        int size = eleList.size();
        int c = size / INSERT_SIZE;
        int r = size % INSERT_SIZE;
        for (int i = 0; i < c; i++) {
            baseMapper.insertBatch(eleList.subList(i * INSERT_SIZE, (i + 1) * INSERT_SIZE));
        }
        if (r > 0) {
            baseMapper.insertBatch(eleList.subList(c * INSERT_SIZE, size));
        }
    }

    @Override
    public List<VppBiddingClearVO.ResolveLoad> getList(ResultAnalysisDTO param, Date dateDay, String startTime, String endTime) {
        return baseMapper.getList(param, dateDay, startTime, endTime);
    }

    @Override
    public List<VppElectricActualVO> getUserRealityPower(ResultAnalysisDTO param, String startTime, String endTime) {
        String month = DateUtil.format(param.getStartDate(), "yyyy-MM");
        return baseMapper.getUserRealityPower(param, startTime, endTime, month);
    }

    private QueryWrapper<VppElectricActualDTO> querySearch(VppBatteryInformationDTO param) {
        QueryWrapper<VppElectricActualDTO> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("sea.tenant_id", param.getTenantId());
        queryWrapper.eq("sea.is_delete", BooleanEnum.FALSE.getCode());
        if (param.getUserCodeList() != null && !param.getUserCodeList().isEmpty()) {
            queryWrapper.in("sea.user_code", param.getUserCodeList());
        }
        if (param.getSearchDate() != null && param.getSearchDate().size() == VppConstant.TWO_NUMBER) {
            queryWrapper.ge("sea.date_day", param.getSearchDate().get(0));
            queryWrapper.le("sea.date_day", param.getSearchDate().get(1));
        }
        if (param.getSearchTime() != null && param.getSearchTime().size() == VppConstant.TWO_NUMBER) {
            switch (param.getType()) {
                case 96:
                    queryWrapper.ge("sea.time_frame", param.getSearchTime().get(0));
                    queryWrapper.le("sea.time_frame", param.getSearchTime().get(1));
                    break;
                case 24:
                    queryWrapper.ge("stc.twenty_four", param.getSearchTime().get(0));
                    queryWrapper.le("stc.twenty_four", param.getSearchTime().get(1));
                    break;
                default:
                    queryWrapper.ge("sea.time_frame", param.getSearchTime().get(0));
                    queryWrapper.le("sea.time_frame", param.getSearchTime().get(1));
                    break;
            }
        }
        if (param.getSelectDate() != null && !param.getSelectDate().isEmpty()) {
            queryWrapper.in("sea.date_day", param.getSelectDate());
        }
        if (param.getSelectTime() != null && param.getSelectTime().size() == VppConstant.TWO_NUMBER) {
            queryWrapper.ge("sea.date_day", MyDateUtil.getMinMonthDate(param.getSelectTime().get(0)));
            queryWrapper.le("sea.date_day", MyDateUtil.getMaxMonthDate(param.getSelectTime().get(1)));
        }

        return queryWrapper;
    }


    @Override
    public List<VppElectricActualSimpleVO> electricListByDateTimeUser(VppBatteryInformationDTO param) {
        //获取绑定用户信息
        List<VppUser> listByTenantId = vppLoadUserService.getListByTenantId(param.getTenantId(), "");
        List<VppUser> userList = new ArrayList<>();
        if (CollUtil.isNotEmpty(listByTenantId)) {
            //绑定周期判断
            userList = vppLoadUserService.getUserListByTwoDate(param.getSearchDate().get(0), param.getSearchDate().get(1), listByTenantId);
        }


        QueryWrapper<VppElectricActualDTO> queryWrapper = this.querySearch(param);
        if (ObjectUtil.isNotEmpty(userList)) {
            List<Long> userIdList = userList.stream().map(VppUser::getId).collect(Collectors.toList());
            queryWrapper.in("user_id", userIdList);
        }
        Date minMonthDate = null;
        Date maxMonthDate = null;
        if (param.getSearchDate() != null && param.getSearchDate().size() == 2) {
            minMonthDate = MyDateUtil.getMinMonthDate(param.getSearchDate().get(0));
            maxMonthDate = MyDateUtil.getMaxMonthDate(param.getSearchDate().get(1));
        }
        if (TimeConstant.NINETY_SIX_NUMBER.equals(param.getType())) {
            // 96个点
            queryWrapper.groupBy("sea.user_code, sea.date_day", "sea.time_frame");
            queryWrapper.orderByAsc("sea.user_code, sea.date_day", "sea.time_frame");
            return baseMapper.selectElectricListByDateTimeUser(queryWrapper, param.getTenantId(), param.getTradingUnit(), minMonthDate, maxMonthDate);
        } else {
            // 24个点
            queryWrapper.groupBy("sea.user_code, sea.date_day", "stc.twenty_four");
            queryWrapper.orderByAsc("sea.user_code, sea.date_day", "stc.twenty_four");
            return baseMapper.select24ElectricListByDateTimeUser(queryWrapper, param.getTenantId(), param.getTradingUnit(), minMonthDate, maxMonthDate);
        }
    }

    @Override
    public List<VppElectricActualVO> electricActualList(ResultAnalysisDTO param, String startTime, String endTime) {
        return baseMapper.electricActualList(param, startTime, endTime);
    }

    @Override
    public List<PieChartOfUserVO.RatioVO> getAllInfo(ControllableLoadDTO param, Long userId, List<Long> userIdList) {
        LambdaQueryWrapper<VppElectricActualConverge> electricConvergeLqw = new LambdaQueryWrapper<>();
        electricConvergeLqw.in(VppElectricActualConverge::getUserId, userIdList);
        return baseMapper.getAllInfo(param, userId, electricConvergeLqw);
    }

    @Override
    public BigDecimal getAllPower(ControllableLoadDTO param, List<Long> userIdList) {
        LambdaQueryWrapper<VppElectricActual> electricConvergeLqw = new LambdaQueryWrapper<>();
        electricConvergeLqw.in(VppElectricActual::getUserId, userIdList);
        return baseMapper.getAllPower(param, electricConvergeLqw);
    }

    @Override
    public List<VppElectricActualForUserVO> getElectricActual(Long userId, Date monthStart, Date monthEnd) {
        return baseMapper.getElectricActual(userId, monthStart, monthEnd);
    }

    @Override
    public List<VppElectricActualVO> electricActualAllList(ResultAnalysisDTO param, List<Long> userId, String startTime, String endTime) {
        return baseMapper.electricActualAllList(param, userId, startTime, endTime);
    }

    @Override
    public List<DividendVO.UserElectricSqlVO> electricActualAll(ResultAnalysisDTO resultAnalysisDTO) {
        return baseMapper.electricActualAll(resultAnalysisDTO);
    }

    @Override
    public String getUpdateTime(GetUpdateTimeDTO param) {
        return baseMapper.getUpdateTime(param);
    }

    @Override
    public List<VppBiddingClearVO.ResolveLoad> getElecList(ResultAnalysisDTO param, String startTime, String endTime) {
        List<VppBiddingClearVO.ResolveLoad> list = new ArrayList<>();
        List<VppElectricActualConverge> vppElectricActuals = vppElectricActualConvergeService.getElecList(param, startTime, endTime);
        for (VppElectricActualConverge actual : vppElectricActuals) {
            VppBiddingClearVO.ResolveLoad load = new VppBiddingClearVO.ResolveLoad();
            load.setPeriod(actual.getTimeFrame());
            load.setVal(actual.getElectricity());
            load.setDateDay(DateUtil.formatDate(actual.getDateDay()));
            list.add(load);
        }
        return list;
    }

    @Override
    public List<VppBiddingClearVO.ResolveLoad> getElecAllList(ResultAnalysisDTO param, String startTime,
                                                              String endTime, List<Long> userIdList, List<String> periodDetails) {
        long t = System.currentTimeMillis();

        List<VppBiddingClearVO.ResolveLoad> list = new ArrayList<>();
        LambdaQueryWrapper<VppElectricActual> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(ObjectUtil.isNotEmpty(userIdList), VppElectricActual::getUserId, userIdList);
        queryWrapper.ge(VppElectricActual::getDateDay, param.getStartDate());
        queryWrapper.le(VppElectricActual::getDateDay, param.getEndDate());
        queryWrapper.ge(ObjectUtil.isNotNull(startTime), VppElectricActual::getTimeFrame, startTime);
        queryWrapper.le(ObjectUtil.isNotNull(startTime), VppElectricActual::getTimeFrame, endTime);
        queryWrapper.eq(VppElectricActual::getIsDelete, 0);
        if (ObjectUtil.isNull(startTime) && ObjectUtil.isNull(endTime) && ObjectUtil.isNotEmpty(periodDetails) && ObjectUtil.isNotNull(periodDetails)) {
            queryWrapper.in(ObjectUtil.isNotEmpty(periodDetails), VppElectricActual::getTimeFrame, periodDetails);
        }
        List<VppElectricActual> vppElectricActuals = baseMapper.selectList(queryWrapper);
        long l = System.currentTimeMillis() - t;
        System.out.println("获取全部用户的实际用电量" + l);
        for (VppElectricActual actual : vppElectricActuals) {
            VppBiddingClearVO.ResolveLoad load = new VppBiddingClearVO.ResolveLoad();
            load.setPeriod(actual.getTimeFrame());
            load.setVal(actual.getElectricity());
            load.setUserId(actual.getUserId());
            load.setDateDay(DateUtil.formatDate(actual.getDateDay()));
            list.add(load);
        }
        long l1 = System.currentTimeMillis() - t;
        System.out.println("组装数据" + l1);
        return list;
    }

    @Override
    public List<VppBiddingClearVO.ResolveLoad> getListByTenantId(Long tenantId, Date dateDay) {
        return baseMapper.getListByTenantId(tenantId, dateDay);
    }

    @Override
    public void removeByParam(Date dateDay, Long tenantId) {
        baseMapper.removeByParam(dateDay, tenantId);
        baseMapper.remove24ByParam(dateDay, tenantId);
    }

    @Override
    public Map<String, List<BigDecimal>> getActualTwentyFourForUser(Date startDay, Date endDay, Long tenantId, List<String> userCodes) {
        //获取用户信息
        List<VppUser> userINfoByUserCode = vppLoadUserService.getUserINfoByUserCode(userCodes, tenantId);
        Map<Long, String> userMap = userINfoByUserCode.stream().collect(Collectors.toMap(VppUser::getId, VppUser::getUserCode));
        List<Long> userIdList = userINfoByUserCode.stream().map(VppUser::getId).collect(Collectors.toList());
        //获取实际用电量
        List<VppElectricActualVO> actualTwentyFourForUser = baseMapper.getActualTwentyFourForUser(startDay, endDay, tenantId, userIdList);

        Map<String, List<BigDecimal>> resultMap = new HashMap<>();
        Map<Long, List<VppElectricActualVO>> collect = actualTwentyFourForUser.stream().collect(Collectors.groupingBy(VppElectricActualVO::getUserId));
        for (Map.Entry<Long, List<VppElectricActualVO>> entry : collect.entrySet()) {
            Long key = entry.getKey();
            String s = userMap.get(key);
            List<BigDecimal> list = new ArrayList<>();
            List<VppElectricActualVO> vppElectricActualVOS = collect.get(key);
            vppElectricActualVOS = vppElectricActualVOS.stream().sorted(Comparator.comparing(VppElectricActualVO::getTimeFrame)).collect(Collectors.toList());
            for (VppElectricActualVO electricActualVO : vppElectricActualVOS) {
                BigDecimal electricity = electricActualVO.getElectricity();
                list.add(electricity);
            }
            resultMap.put(s, list);
        }
        System.out.println(resultMap);
        return resultMap;
    }


    @Override
    public ImportExcelDetailVO importVppElectricActualListBatch(List<VppElectricActualDTO> allActualDTOs, List<VppElectricQuantityDTO> params, List<DataMaintenanceUpdateDTO> updateDTOs) {
        ImportExcelDetailVO importExcelDetail = new ImportExcelDetailVO();
        importExcelDetail.setInfoList(new ArrayList<>());

        int success = 0;
        int failed = 0;

        Map<String, List<DataMaintenanceUpdateDTO>> updateMaps = updateDTOs.stream().collect(Collectors.groupingBy(o -> DateUtil.formatDate(o.getDateDay())));


        Map<String, List<VppElectricActualDTO>> allActualDTOMap = allActualDTOs.stream().collect(Collectors.groupingBy(o -> DateUtil.formatDate(o.getDateDay())));
        // 按租户ID分组处理
        Map<Long, List<VppElectricQuantityDTO>> tenantToParamsMap = params.stream()
                .collect(Collectors.groupingBy(VppElectricQuantityDTO::getTenantId));

        for (Map.Entry<Long, List<VppElectricQuantityDTO>> entry : tenantToParamsMap.entrySet()) {
            Long tenantId = entry.getKey();
            List<VppElectricQuantityDTO> tenantParams = entry.getValue();

            // 获取租户下的用户列表
            List<VppUser> userList = vppLoadUserService.getListByTenantId(tenantId, "");
            Map<String, VppUser> userNameMap = userList.stream()
                    .collect(Collectors.toMap(VppUser::getName, u -> u));
            List<String> nameList = userList.stream().map(VppUser::getName).collect(Collectors.toList());

            // 按日期分组处理
            Map<Date, List<VppElectricQuantityDTO>> dateToParamsMap = tenantParams.stream()
                    .collect(Collectors.groupingBy(VppElectricQuantityDTO::getRunningDate));

            for (Map.Entry<Date, List<VppElectricQuantityDTO>> dateEntry : dateToParamsMap.entrySet()) {
                Date runningDate = dateEntry.getKey();
                List<VppElectricQuantityDTO> dateParams = dateEntry.getValue();

                // 创建分区表
                vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL, runningDate);

                // 处理每个日期的数据
                List<VppElectricActual> electricActualList = new ArrayList<>();
                List<BigDecimal> electricList = new ArrayList<>();

                List<VppElectricActualDTO> vppElectricActualDTOS = allActualDTOMap.get(DateUtil.formatDate(runningDate));

                vppElectricActualDTOS = vppElectricActualDTOS.stream().filter(o -> nameList.contains(o.getName())).collect(Collectors.toList());

                for (VppElectricQuantityDTO param : dateParams) {
                    for (VppElectricActualDTO vppElectricActualDTO : vppElectricActualDTOS) {
                        String userName = vppElectricActualDTO.getName();
                        VppUser vppUser = userNameMap.get(userName);
                        vppElectricActualDTO.setUserCode(vppUser.getUserCode());
                        vppElectricActualDTO.setUserId(vppUser.getId());

                        VppElectricActual vppElectricActual = new VppElectricActual();
                        BeanCopyUtils.copy(vppElectricActualDTO, vppElectricActual);
                        vppElectricActual.setId(IdWorker.getId());
                        vppElectricActual.setIsDelete(0);
                        vppElectricActual.setElectricId(param.getId());
                        electricActualList.add(vppElectricActual);
                        electricList.add(vppElectricActual.getElectricity());
                    }
                }

                success += electricActualList.size();

                // 校验数据
                if (electricList.isEmpty()) {
                    importExcelDetail.getInfoList().add("异常原因：" +
                            VirtualPowerPlantServiceCodeEnum.IMPORT_NO_BIND_USER.getMessage());
                    failed += 1;
                }

                if (electricList.size() % VppConstant.NINETY_SIX_NUMBER != 0) {
                    importExcelDetail.getInfoList().add("异常原因：" +
                            VirtualPowerPlantServiceCodeEnum.IMPORT_QUANTITY_ERROR.getMessage());
                    failed += 1;
                }

                if (failed > 0) {
                    // 如果有异常，手动回滚
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                    return importExcelDetail;
                } else {
//                    List<Long> updateList = new ArrayList<>();
//                    // 判断是否已经存在，已经存在进行修改操作，如果没有的话在直接新增
//                    if (ObjectUtil.isNotEmpty(dateParams)) {
//                        Long id = dateParams.get(0).getId();
//
//                        // 优化查询，只查询需要的字段
//                        LambdaQueryWrapper<VppElectricActual> queryWrapper = new LambdaQueryWrapper<>();
//                        queryWrapper.eq(VppElectricActual::getElectricId, id)
//                                .select(VppElectricActual::getId, VppElectricActual::getName, VppElectricActual::getTimeFrame);
//                        List<VppElectricActual> oldElectricActualList = this.list(queryWrapper);
//
//                        // 使用 HashMap 构建 oldElectricActualMap
//                        Map<String, Map<String, VppElectricActual>> oldElectricActualMap = new HashMap<>();
//                        for (VppElectricActual oldData : oldElectricActualList) {
//                            oldElectricActualMap.computeIfAbsent(oldData.getName(), k -> new HashMap<>())
//                                    .put(oldData.getTimeFrame(), oldData);
//                        }
//
//                        // 使用 HashMap 构建 electricActualMap
//                        Map<String, Map<String, VppElectricActual>> electricActualMap = new HashMap<>();
//                        for (VppElectricActual newData : electricActualList) {
//                            electricActualMap.computeIfAbsent(newData.getName(), k -> new HashMap<>())
//                                    .put(newData.getTimeFrame(), newData);
//                        }
//
//                        // 并行比较和更新
//                        electricActualMap.forEach((name, dataList) -> {
//                            if (oldElectricActualMap.containsKey(name)) {
//                                // 旧数据进行更新
//                                Map<String, VppElectricActual> electricActualDataMap = oldElectricActualMap.get(name);
//                                // 遍历新数据 将旧数据的ID赋过去
//
//                                for (Map.Entry<String, VppElectricActual> actualEntry : dataList.entrySet()) {
//                                    String timeFrame = actualEntry.getKey();
//                                    // 判断之前是否有上传的
//                                    VppElectricActual oldData = electricActualDataMap.get(timeFrame);
//                                    updateList.add(oldData.getId());
//                                }
//                            }
//                        });
//                    }
//                    if (ObjectUtil.isNotEmpty(updateList)) {
//                        baseMapper.removeByIdList(updateList);
//                    }
                    saveBatch(electricActualList);
                }
            }
        }

        importExcelDetail.setSuccessNum(success);
        importExcelDetail.setFailedNum(failed);

        if (failed > 0) {
            importExcelDetail.setMessage("部分数据内容异常");
            importExcelDetail.setStatus(false);
        } else {
            importExcelDetail.setStatus(true);


            ExecutorService executor = Executors.newFixedThreadPool(10); // 创建一个固定大小的线程池

            CompletableFuture<?>[] futures = new CompletableFuture[params.size()];

            for (int i = 0; i < params.size(); i++) {
                VppElectricQuantityDTO param = params.get(i);

                futures[i] = CompletableFuture.runAsync(() -> {
                    vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_24, param.getRunningDate());
                    vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_CONVERGE, param.getRunningDate());

                    int months = DateUtil.dayOfMonth(param.getRunningDate()) > 27 ? 3 : 2;
                    for (int j = 0; j < months; j++) {
                        vppPartitionTableService.createPartitionTable(PartitionTableConstant.VPP_ELECTRIC_ACTUAL_MIDDLE,
                                DateUtil.offsetMonth(param.getRunningDate(), j));
                    }

                    baseMapper.importElectricCalculate(param.getTenantId(), param.getRunningDate(), param.getId());

                    // 批量更新数据维护状态
                    List<DataMaintenanceUpdateDTO> dataMaintenanceUpdateDTOS = updateMaps.get(DateUtil.formatDate(param.getRunningDate()));
                    if (ObjectUtil.isNotEmpty(dataMaintenanceUpdateDTOS)) {
                        dataMaintenanceService.updateStatus(dataMaintenanceUpdateDTOS.get(0));
                    }
                }, executor); // 使用自定义的线程池
            }

//            CompletableFuture.allOf(futures).join();
            executor.shutdown();
        }

        return importExcelDetail;
    }

    @Override
    public List<VppElectricActual> getElectricByDateList(Date startTime, Date endTime, Long tenantId, String consNo) {
        LambdaQueryWrapper<VppElectricActual> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ObjectUtil.isNotNull(consNo), VppElectricActual::getRegistered, consNo);
        queryWrapper.ge(VppElectricActual::getDateDay, startTime);
        queryWrapper.le(VppElectricActual::getDateDay, endTime);
        queryWrapper.le(VppElectricActual::getTenantId, tenantId);
        return baseMapper.selectList(queryWrapper);
    }

}
