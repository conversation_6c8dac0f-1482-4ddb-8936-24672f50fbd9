package com.fx.green.electricity.shanxi.service.service.electric;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualMiddle;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
public interface VppElectricActualMiddleService extends IService<VppElectricActualMiddle> {


    /**
     * 根据社会信用代码集合查询中位数
     * @param userIdList 用户id列表
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param tenantId 租户id
     * @return 中位数集合列表
     */
    List<VppElectricActualMiddle> queryByUserIdList(List<Long> userIdList,
                                                      Date startTime,
                                                      Date endTime,
                                                      Long tenantId);

    void removeByParam(Date dateDay, Long tenantId);
}
