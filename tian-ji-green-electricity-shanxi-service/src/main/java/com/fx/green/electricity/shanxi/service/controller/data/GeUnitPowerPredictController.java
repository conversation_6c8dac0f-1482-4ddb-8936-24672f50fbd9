package com.fx.green.electricity.shanxi.service.controller.data;

import com.fx.green.electricity.shanxi.service.service.data.GeUnitPowerPredictService;
import io.swagger.annotations.Api;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 机组功率预测
 */
@RestController
@Api(tags = "机组功率预测")
@RequestMapping("geUnitPowerPredict")
public class GeUnitPowerPredictController {

    @Autowired
    private GeUnitPowerPredictService geUnitPowerPredictService;
}
