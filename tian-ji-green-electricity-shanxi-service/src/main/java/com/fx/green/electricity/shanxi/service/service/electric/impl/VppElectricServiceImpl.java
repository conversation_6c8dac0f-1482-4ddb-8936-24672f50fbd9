package com.fx.green.electricity.shanxi.service.service.electric.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.TimeConstant;
import com.fx.common.enums.BooleanEnum;
import com.fx.data.gather.api.api.DataGatherVppApi;
import com.fx.data.gather.api.dto.vpp.VppUserDataQueryDTO;
import com.fx.data.gather.api.vo.vpp.PubVppDataVO;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActualConverge;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContractsManage;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualConvergeService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricService;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsManageService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import com.fx.green.electricity.shanxi.service.utils.TimeUtils;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.ExcessMLDeclareCalDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.FaceElectricityVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 公共电量service
 *
 * <AUTHOR>
 **/
@Slf4j
@Service
public class VppElectricServiceImpl implements VppElectricService {
    private static final String DATA_SPILT = "卍";

    @Resource
    private VppElectricActualConvergeService vppElectricActualConvergeService;
    @Resource
    private VppRetailContractsManageService vppRetailContractsManageService;
    @Resource
    private VppLoadUserService vppLoadUserService;
    @Resource
    private DataGatherVppApi dataGatherVppApi;

    @Override
    public Map<Long, Map<String, Map<String, BigDecimal>>> getElectricMap(Long tenantId, List<VppUserDTO> userList, Date startDate, Date endDate) {
        // 如果userIdList是空 通过tenantId获取用户列表
        long t = System.currentTimeMillis();

        List<Long> userIdList;
        List<String> userCodeList;
        Map<String, Long> userMap;
        // 组装数据
        Map<Long, Map<String, Map<String, BigDecimal>>> result = new LinkedHashMap<>();
        if (ObjectUtil.isNull(userList)) {
            List<VppUser> vppUserList = vppLoadUserService.getListByTenantId(tenantId, null);
            //绑定周期判断
            List<VppUser> userListByTwoDate = vppLoadUserService.getUserListByTwoDate(startDate, endDate, vppUserList);
            List<VppUserDTO> vppUserDTOS = BeanUtil.
                    copyToList(vppLoadUserService.getUserListByTwoDate(startDate, endDate, userListByTwoDate), VppUserDTO.class);
            userIdList = vppUserDTOS.stream().map(VppUserDTO::getId).collect(Collectors.toList());
            userCodeList = vppUserDTOS.stream().map(VppUserDTO::getUserCode).collect(Collectors.toList());
            userMap = vppUserDTOS.stream().collect(Collectors.toMap(VppUserDTO::getUserCode, VppUserDTO::getId));
        } else {
            userIdList = userList.stream().map(VppUserDTO::getId).collect(Collectors.toList());
            userCodeList = userList.stream().map(VppUserDTO::getUserCode).collect(Collectors.toList());
            userMap = userList.stream().collect(Collectors.toMap(VppUserDTO::getUserCode, VppUserDTO::getId));
        }

        // 查询所有的实际用电量
        if (ObjectUtil.isNotEmpty(userIdList)) {
            LambdaQueryWrapper<VppElectricActualConverge> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ge(VppElectricActualConverge::getDateDay, DateUtil.beginOfDay(startDate));
            queryWrapper.le(VppElectricActualConverge::getDateDay, DateUtil.endOfDay(endDate));
            queryWrapper.in(VppElectricActualConverge::getUserId, userIdList);
            queryWrapper.eq(VppElectricActualConverge::getIsDelete, BooleanEnum.FALSE.getCode());
            queryWrapper.select(VppElectricActualConverge::getUserId, VppElectricActualConverge::getDateDay,
                    VppElectricActualConverge::getTimeFrame, VppElectricActualConverge::getElectricity);
            List<VppElectricActualConverge> electricActualList = vppElectricActualConvergeService.list(queryWrapper);

            // 按日期进行分组，并按userId去重
            Map<String, Set<Long>> dateUserIdMap = electricActualList.stream()
                    .collect(Collectors.groupingBy(o -> DateUtil.formatDate(o.getDateDay()),
                            Collectors.mapping(VppElectricActualConverge::getUserId, Collectors.toSet())));

            // 找出要去获取关口表数据的日期
            List<String> gatherDateList = new ArrayList<>();
            long dateInterval = DateUtil.betweenDay(startDate, endDate, true);
            for (int i = 0; i <= dateInterval; i++) {
                DateTime date = DateUtil.offsetDay(startDate, i);
                String s = DateUtil.formatDate(date);
                // 如果实际用电量当天数据是空的，或者实际用电量的数量和用户的数量对不上 找出这些日期
                if (!dateUserIdMap.containsKey(s) || dateUserIdMap.get(s).size() != userIdList.size()) {
                    gatherDateList.add(date.toDateStr());
                }
            }

            // 关口表电量组装
            Map<Long, Map<String, List<PubVppDataVO>>> dataGatherMap = new HashMap<>();
            if (ObjectUtil.isNotEmpty(gatherDateList)) {
                // 获取关口表数据
//                PubDeviceDataSearchDTO pubDeviceDataSearchDTO = new PubDeviceDataSearchDTO();
//                pubDeviceDataSearchDTO.setDateList(gatherDateList);
//                pubDeviceDataSearchDTO.setUserCodeList(userCodeList);
//                pubDeviceDataSearchDTO.setTankType("virtualPowerPlant");
//                log.info("VppElectricServiceImpl-getElectricMap getPubDeviceDataElectricityByTime96 param: " + pubDeviceDataSearchDTO);
//                List<PubDeviceDataElectricityVO> dataList = dataGatherOperationApi.getPubDeviceDataElectricityByTime96DateList(
//                        pubDeviceDataSearchDTO).successData();


                VppUserDataQueryDTO vppDataQueryDTO = new VppUserDataQueryDTO();
                vppDataQueryDTO.setTenantId(tenantId);
                vppDataQueryDTO.setStartTime(startDate);
                vppDataQueryDTO.setEndTime(DateUtil.offsetDay(endDate, 1).toJdkDate());
                vppDataQueryDTO.setGroupType(15);
                List<PubVppDataVO> dataList = dataGatherVppApi.queryUserElectricityBy15(vppDataQueryDTO).successData();


                for (PubVppDataVO vo : dataList) {
                    if (ObjectUtil.isNull(vo.getElectricity())) {
                        vo.setElectricity(BigDecimal.ZERO);
                    }
                }
                // 关口表电量组装
                dataList = dataList.stream().filter(o -> userCodeList.contains(o.getUserCode())).collect(Collectors.toList());
                dataGatherMap = dataList.stream().
                        collect(Collectors.groupingBy(d -> userMap.get(d.getUserCode()),
                                Collectors.groupingBy(PubVppDataVO::getDateStr)));

            }

            // 解析数据组装
            // 实际用电量组装
            Map<Long, Map<String, List<VppElectricActualConverge>>> actualMap = electricActualList.stream()
                    .collect(Collectors.groupingBy(VppElectricActualConverge::getUserId,
                            Collectors.groupingBy(d -> DateUtil.format(d.getDateDay(), DatePattern.NORM_DATE_FORMAT))));


            Map<String, BigDecimal> emptyMap = new HashMap<>();
            for (String ninetySixTime : TimeConstant.NINETY_SIX_TIMES) {
                emptyMap.put(ninetySixTime, null);
            }
            for (Long userId : userIdList) {
                // 给用户添加日期的Map
                Map<String, Map<String, BigDecimal>> dateMap = new LinkedHashMap<>();
                result.put(userId, dateMap);

                for (int i = 0; i <= dateInterval; i++) {
                    // 给日期添加时点的Map
                    DateTime date = DateUtil.offsetDay(startDate, i);
                    String dateStr = date.toDateStr();

                    Map<String, BigDecimal> dataMap;
                    if (actualMap.containsKey(userId) && actualMap.get(userId).containsKey(dateStr)) {
                        // 实际用电量中如果有
                        dataMap = actualMap.get(userId).get(dateStr).stream().collect(Collectors.toMap(
                                VppElectricActualConverge::getTimeFrame, VppElectricActualConverge::getElectricity));
                    } else if (dataGatherMap.containsKey(userId) && dataGatherMap.get(userId).containsKey(dateStr)) {
                        // 关口表中如果有
                        dataMap = dataGatherMap.get(userId).get(dateStr).stream()
                                .collect(Collectors.toMap(PubVppDataVO::getTimeFrame, PubVppDataVO::getElectricity));
                    } else {
                        // 哪儿都没有
                        dataMap = emptyMap;
                    }
                    dateMap.put(dateStr, dataMap);
                }
            }
        }
        long l = System.currentTimeMillis() - t;
        System.out.println("获取数据用时====" + l);
        return result;
    }


    @Override
    public Map<Long, Map<String, Map<String, BigDecimal>>> getElectricNewMap(Long tenantId, List<VppUserDTO> userList, Date startDate, Date endDate) {
        // 如果userIdList是空 通过tenantId获取用户列表

        List<Long> userIdList;
        List<String> userCodeList;
        Map<String, Long> userMap;
        // 组装数据
        Map<Long, Map<String, Map<String, BigDecimal>>> result = new LinkedHashMap<>();
        if (ObjectUtil.isNull(userList)) {
            List<VppUser> vppUserList = vppLoadUserService.getListByTenantId(tenantId, null);
            //绑定周期判断
            List<VppUser> userListByTwoDate = vppLoadUserService.getUserListByTwoDate(startDate, endDate, vppUserList);
            List<VppUserDTO> vppUserDTOS = BeanUtil.
                    copyToList(vppLoadUserService.getUserListByTwoDate(startDate, endDate, userListByTwoDate), VppUserDTO.class);
            userIdList = vppUserDTOS.stream().map(VppUserDTO::getId).collect(Collectors.toList());
            userCodeList = vppUserDTOS.stream().map(VppUserDTO::getUserCode).collect(Collectors.toList());
            userMap = vppUserDTOS.stream().collect(Collectors.toMap(VppUserDTO::getUserCode, VppUserDTO::getId));
        } else {
            userIdList = userList.stream().map(VppUserDTO::getId).collect(Collectors.toList());
            userCodeList = userList.stream().map(VppUserDTO::getUserCode).collect(Collectors.toList());
            userMap = userList.stream().collect(Collectors.toMap(VppUserDTO::getUserCode, VppUserDTO::getId));
        }

        // 查询所有的实际用电量
        if (ObjectUtil.isNotEmpty(userIdList)) {
            LambdaQueryWrapper<VppElectricActualConverge> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.ge(VppElectricActualConverge::getDateDay, DateUtil.beginOfDay(startDate));
            queryWrapper.le(VppElectricActualConverge::getDateDay, DateUtil.endOfDay(endDate));
            queryWrapper.in(VppElectricActualConverge::getUserId, userIdList);
            queryWrapper.eq(VppElectricActualConverge::getIsDelete, BooleanEnum.FALSE.getCode());
            queryWrapper.select(VppElectricActualConverge::getUserId, VppElectricActualConverge::getDateDay,
                    VppElectricActualConverge::getTimeFrame, VppElectricActualConverge::getElectricity);
            List<VppElectricActualConverge> electricActualList = vppElectricActualConvergeService.list(queryWrapper);

            // 按日期进行分组，并按userId去重
            Map<String, Set<Long>> dateUserIdMap = electricActualList.stream()
                    .collect(Collectors.groupingBy(o -> DateUtil.formatDate(o.getDateDay()),
                            Collectors.mapping(VppElectricActualConverge::getUserId, Collectors.toSet())));

            // 找出要去获取关口表数据的日期
            List<String> gatherDateList = new ArrayList<>();
            long dateInterval = DateUtil.betweenDay(startDate, endDate, true);
            for (int i = 0; i <= dateInterval; i++) {
                DateTime date = DateUtil.offsetDay(startDate, i);
                String s = DateUtil.formatDate(date);
                // 如果实际用电量当天数据是空的，或者实际用电量的数量和用户的数量对不上 找出这些日期
                if (!dateUserIdMap.containsKey(s) || dateUserIdMap.get(s).size() != userIdList.size()) {
                    gatherDateList.add(date.toDateStr());
                }
            }

            // 关口表电量组装
            Map<Long, Map<String, List<PubVppDataVO>>> dataGatherMap = new HashMap<>();
            if (ObjectUtil.isNotEmpty(gatherDateList)) {
                // 获取关口表数据
                VppUserDataQueryDTO vppDataQueryDTO = new VppUserDataQueryDTO();
                vppDataQueryDTO.setTenantId(tenantId);
                vppDataQueryDTO.setStartTime(startDate);
                vppDataQueryDTO.setEndTime(DateUtil.offsetDay(endDate, 1).toJdkDate());
                vppDataQueryDTO.setGroupType(15);
                DataResult<List<PubVppDataVO>> dataResult = dataGatherVppApi.queryUserElectricityBy15(vppDataQueryDTO);


                List<PubVppDataVO> dataList = dataResult.getData();
                if (dataList != null) {
                    for (PubVppDataVO vo : dataList) {
                        if (ObjectUtil.isNull(vo.getElectricity())) {
                            vo.setElectricity(BigDecimal.ZERO);
                        }
                    }
                    dataList = dataList.stream().filter(o -> userCodeList.contains(o.getUserCode())).collect(Collectors.toList());
                    // 关口表电量组装
                    dataGatherMap = dataList.stream().
                            collect(Collectors.groupingBy(d -> userMap.get(d.getUserCode()),
                                    Collectors.groupingBy(PubVppDataVO::getDateStr)));
                }
            }

            // 解析数据组装
            // 实际用电量组装
            Map<Long, Map<String, List<VppElectricActualConverge>>> actualMap = electricActualList.stream()
                    .collect(Collectors.groupingBy(VppElectricActualConverge::getUserId,
                            Collectors.groupingBy(d -> DateUtil.format(d.getDateDay(), DatePattern.NORM_DATE_FORMAT))));


            Map<String, BigDecimal> emptyMap = new HashMap<>();
            for (String ninetySixTime : TimeConstant.NINETY_SIX_TIMES) {
                emptyMap.put(ninetySixTime, null);
            }
            for (Long userId : userIdList) {
                // 给用户添加日期的Map
                Map<String, Map<String, BigDecimal>> dateMap = new LinkedHashMap<>();
                result.put(userId, dateMap);

                for (int i = 0; i <= dateInterval; i++) {
                    // 给日期添加时点的Map
                    DateTime date = DateUtil.offsetDay(startDate, i);
                    String dateStr = date.toDateStr();

                    Map<String, BigDecimal> dataMap;
                    if (actualMap.containsKey(userId) && actualMap.get(userId).containsKey(dateStr)) {
                        // 实际用电量中如果有
                        dataMap = actualMap.get(userId).get(dateStr).stream().collect(Collectors.toMap(
                                VppElectricActualConverge::getTimeFrame, VppElectricActualConverge::getElectricity));
                    } else if (dataGatherMap.containsKey(userId) && dataGatherMap.get(userId).containsKey(dateStr)) {
                        // 关口表中如果有
                        dataMap = dataGatherMap.get(userId).get(dateStr).stream()
                                .collect(Collectors.toMap(PubVppDataVO::getTimeFrame96, PubVppDataVO::getElectricity));
                    } else {
                        // 哪儿都没有
                        dataMap = emptyMap;
                    }
                    dateMap.put(dateStr, dataMap);
                }
            }
        }
        return result;
    }

    @Override
    public List<VppElectricActualConverge> getElectricByDate(ExcessMLDeclareCalDTO param) {
        return vppElectricActualConvergeService.getElectricByDate(param);
    }


    @Override
    public Map<String, List<FaceElectricityVO>> getAllElecPrice(Date startTime, Date endTime, Long tenantId, Integer type, Map<String, List<Date>> dateMap, Map<String, List<String>> timeMap) {
        Map<String, List<Long>> userInfoMap = new HashMap<>();

        List<VppUser> vppUserList = vppLoadUserService.getListByTenantId(tenantId, "");
        //判断绑定周期
        List<VppUser> userList = vppLoadUserService.getUserListByTwoDate(startTime, endTime, vppUserList);
        List<VppUserDTO> vppUserDTOS = BeanUtil.copyToList(userList, VppUserDTO.class);
        for (Map.Entry<String, List<Date>> entry : dateMap.entrySet()) {
            String key = entry.getKey();
            DateTime parse = DateUtil.parse(key + "-01", "yyyy-MM-dd");
            List<VppUser> userLists = vppLoadUserService.getUserList(parse, vppUserList);
            userInfoMap.put(key, userLists.stream().map(o -> o.getId()).collect(Collectors.toList()));
        }
        // 组合数据
        Map<String, List<FaceElectricityVO>> result = new HashMap<>();

        //获取全部用户实际用电量
        Map<Long, Map<String, Map<String, BigDecimal>>> electricMap = this.getElectricNewMap(
                tenantId, vppUserDTOS, startTime, endTime);

        //获取用户的电价
        Map<Long, Map<String, Map<String, BigDecimal>>> priceMap = new HashMap<>();
        List<Long> userIdList = userList.stream().map(o -> o.getId()).collect(Collectors.toList());
        if (ObjectUtil.isNotEmpty(userIdList)) {
            LambdaQueryWrapper<VppRetailContractsManage> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.in(VppRetailContractsManage::getUserId, userIdList);
            queryWrapper.ge(VppRetailContractsManage::getSigningMonth, DateUtil.beginOfMonth(startTime));
            queryWrapper.le(VppRetailContractsManage::getSigningMonth, DateUtil.endOfMonth(endTime));
            queryWrapper.eq(VppRetailContractsManage::getIsDelete, 0);
            queryWrapper.eq(VppRetailContractsManage::getType, 2);
            List<VppRetailContractsManage> priceList = vppRetailContractsManageService.list(queryWrapper);
            List<VppRetailContractsManage> tempPriceList = new ArrayList<>();
            for (VppRetailContractsManage vppRetailContractsManage : priceList) {
                String timeFrame = vppRetailContractsManage.getTimeFrame();
                List<String> timeFrameList = TimeUtils.point24To96(timeFrame);
                for (String t : timeFrameList) {
                    VppRetailContractsManage retailContractsManage = BeanUtil.copyProperties(vppRetailContractsManage, VppRetailContractsManage.class);
                    retailContractsManage.setTimeFrame(t);
                    tempPriceList.add(retailContractsManage);
                }
            }
            priceMap = tempPriceList.stream()
                    .collect(Collectors.groupingBy(VppRetailContractsManage::getUserId,
                            Collectors.groupingBy(d -> DateUtil.format(d.getSigningMonth(), DatePattern.NORM_DATE_FORMAT),
                                    Collectors.toMap(VppRetailContractsManage::getTimeFrame,
                                            VppRetailContractsManage::getVal))));
        }

        long dateInterval = DateUtil.betweenDay(startTime, endTime, true);
        for (VppUserDTO user : vppUserDTOS) {
            Long userId = user.getId();
            for (int i = 0; i <= dateInterval; i++) {
                DateTime date = DateUtil.offsetDay(startTime, i);
                String dateStr = date.toDateStr();

                // 时段选择
                DateTime month = DateUtil.parseDate(dateStr);
                String monthStr = month.toDateStr();
                if (timeMap.containsKey(monthStr)) {
                    List<String> timeList = timeMap.get(monthStr);
                    FaceElectricityVO faceElectricityVO = new FaceElectricityVO();
                    faceElectricityVO.setUserId(userId);

                    Map<String, BigDecimal> faceElectricityNumber = new HashMap<>();
                    Map<String, BigDecimal> price = new HashMap<>();
                    for (String time : timeList) {
                        // 电量选择
                        if (electricMap.containsKey(userId) && electricMap.get(userId).containsKey(dateStr)
                                && electricMap.get(userId).get(dateStr).containsKey(time)) {
                            faceElectricityNumber.put(dateStr + DATA_SPILT + time, electricMap.get(userId).get(dateStr).get(time));
                        } else {
                            faceElectricityNumber.put(dateStr + DATA_SPILT + time, BigDecimal.ZERO);
                        }
                        // 价格选择
                        if (priceMap.containsKey(userId) && priceMap.get(userId).containsKey(DateUtil.formatDate(DateUtil.beginOfMonth(month)))
                                && priceMap.get(userId).get(DateUtil.formatDate(DateUtil.beginOfMonth(month))).containsKey(time)) {
                            price.put(dateStr + DATA_SPILT + time, priceMap.get(userId).get(DateUtil.formatDate(DateUtil.beginOfMonth(month))).get(time));
                        } else {
                            price.put(dateStr + DATA_SPILT + time, null);
                        }
                    }
                    faceElectricityVO.setFaceElectricityNumber(faceElectricityNumber);
                    faceElectricityVO.setPriceMap(price);
                    // 保存数据
                    if (!result.containsKey(dateStr)) {
                        result.put(dateStr, new ArrayList<>());
                    }
                    List<FaceElectricityVO> faceElectricityVOList = result.get(dateStr);
                    faceElectricityVOList.add(faceElectricityVO);
                }
            }
        }
        if (dateMap.size() > 1) {
            for (Map.Entry<String, List<FaceElectricityVO>> entry : result.entrySet()) {
                String key = entry.getKey();
                String month = DateUtil.format(DateUtil.parseDate(key), "yyyy-MM");
                List<Long> list = userInfoMap.get(month);
                List<FaceElectricityVO> faceElectricityVOS = result.get(key);
                List<FaceElectricityVO> collect = faceElectricityVOS.stream().filter(o -> list.contains(o.getUserId())).collect(Collectors.toList());
                result.put(key, collect);
            }
        }
        return result;
    }


}
