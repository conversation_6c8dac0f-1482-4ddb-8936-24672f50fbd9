package com.fx.green.electricity.shanxi.service.service.data.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceQueryDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.DataMaintenanceVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ElectricityDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.data.ExpireUserVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppBiddingClearVO;
import com.fx.green.electricity.shanxi.service.constant.VppConstant;
import com.fx.green.electricity.shanxi.service.entity.data.VppDataMaintenance;
import com.fx.green.electricity.shanxi.service.entity.user.VppUser;
import com.fx.green.electricity.shanxi.service.entity.user.VppUserBindCycle;
import com.fx.green.electricity.shanxi.service.enums.DecimalPlaceEnum;
import com.fx.green.electricity.shanxi.service.mapper.data.DataMaintenanceMapper;
import com.fx.green.electricity.shanxi.service.service.data.DataMaintenanceService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualConvergeService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualMiddleService;
import com.fx.green.electricity.shanxi.service.service.electric.VppElectricActualService;
import com.fx.green.electricity.shanxi.service.service.user.VppBindCycleService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import com.fx.green.electricity.shanxi.service.utils.DecimalPlaceUtil;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class DataMaintenanceServiceImpl extends ServiceImpl<DataMaintenanceMapper, VppDataMaintenance> implements DataMaintenanceService {

    @Resource
    private VppLoadUserService vppLoadUserService;

    @Resource
    private VppBindCycleService vppBindCycleService;

    @Resource
    private VppElectricActualService vppElectricActualService;

    @Resource
    private VppElectricActualConvergeService vppElectricActualConvergeService;

    @Resource
    private VppElectricActualMiddleService vppElectricActualMiddleService;

    @Resource
    private DataMaintenanceService dataMaintenanceService;


    @Override
    public List<DataMaintenanceVO> queryList(DataMaintenanceDTO param) {
        LambdaQueryWrapper<VppDataMaintenance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VppDataMaintenance::getDateMonth, param.getQueryMonth());
        wrapper.eq(VppDataMaintenance::getTenantId, param.getTenantId());
        List<VppDataMaintenance> list = list(wrapper);
        List<DataMaintenanceVO> dataMaintenanceVOS = BeanUtil.copyToList(list, DataMaintenanceVO.class);
        dataMaintenanceVOS.forEach(e -> {
            int mediumLongTermStatus = e.getMediumLongTermStatus() == 1 ? e.getMediumLongTermStatus() : 0;
            int declareStatus = e.getDeclareStatus() == 1 ? e.getDeclareStatus() : 0;
            int realElctricityStatus = e.getRealElctricityStatus() == 1 ? e.getRealElctricityStatus() : 0;
            int clearResultStatus = e.getClearResultStatus() == 1 ? e.getClearResultStatus() : 0;
            BigDecimal add = NumberUtil.add(mediumLongTermStatus, declareStatus, realElctricityStatus, clearResultStatus);
            e.setProportion(DecimalPlaceUtil.divide(add, new BigDecimal(4), DecimalPlaceEnum.PRICE));
        });
        return dataMaintenanceVOS;
    }

    @Override
    public List<ExpireUserVO> getExpireUserList(DataMaintenanceDTO param) {
        LambdaQueryWrapper<VppUserBindCycle> bindCycleWrapper = new LambdaQueryWrapper<>();
        Date date = param.getQueryMonth();
        DateTime queryStart = DateUtil.beginOfMonth(date);
        DateTime queryEnd = DateUtil.endOfMonth(date);
        bindCycleWrapper.le(VppUserBindCycle::getBindCycleEnd, queryEnd);
        bindCycleWrapper.ge(VppUserBindCycle::getBindCycleEnd, queryStart);
        bindCycleWrapper.eq(VppUserBindCycle::getTenantId, param.getTenantId());
        List<VppUserBindCycle> list = vppBindCycleService.list(bindCycleWrapper);
        List<ExpireUserVO> resultList = new ArrayList<>();
        if (!list.isEmpty()) {
            LambdaQueryWrapper<VppUser> baseWrapper = new LambdaQueryWrapper<>();
            baseWrapper.in(VppUser::getId, list.stream().map(VppUserBindCycle::getUserId).collect(Collectors.toList()));
            List<VppUser> vppUsers = vppLoadUserService.list(baseWrapper);
            Map<Long, List<VppUserBindCycle>> listMap = list.stream().collect(Collectors.groupingBy(VppUserBindCycle::getUserId));
            for (VppUser vppUser : vppUsers) {
                List<VppUserBindCycle> vppUserBindList = listMap.get(vppUser.getId());
                if (vppUserBindList != null && !vppUserBindList.isEmpty()) {
                    ExpireUserVO expireUserVO = new ExpireUserVO();
                    expireUserVO.setName(vppUser.getName());
                    expireUserVO.setExpireDate(vppUserBindList.get(0).getBindCycleEnd());
                    resultList.add(expireUserVO);
                }
            }
        }
        return resultList;
    }

    @Override
    public Integer geUserCount(DataMaintenanceDTO param) {
        LambdaQueryWrapper<VppUserBindCycle> bindCycleWrapper = new LambdaQueryWrapper<>();
        Date date = param.getQueryMonth();
        bindCycleWrapper.le(VppUserBindCycle::getBindCycleStart, date);
        bindCycleWrapper.ge(VppUserBindCycle::getBindCycleEnd, date);
        bindCycleWrapper.eq(VppUserBindCycle::getTenantId, param.getTenantId());
        return vppBindCycleService.count(bindCycleWrapper);
    }

    @Override
    public Date getdateDay(Long tenantId) {
        LambdaQueryWrapper<VppDataMaintenance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VppDataMaintenance::getTenantId, tenantId);
        wrapper.ne(VppDataMaintenance::getRealElctricityStatus, 2);
        wrapper.orderByAsc(VppDataMaintenance::getDateDay);
        List<VppDataMaintenance> list = list(wrapper);
        if (ObjectUtil.isNotEmpty(list)) {
            return list.get(list.size() - 1).getDateDay();
        } else {
            return null;
        }
    }

    @Override
    public List<ElectricityDetailVO> getElectricDetail(DataMaintenanceQueryDTO param) {

        // 查询虚拟电厂下负荷用户列表
        List<VppUser> vppUserLists = vppLoadUserService.getListByTenantId(param.getTenantId(), null);
        //绑定周期判断
        DateTime startDate = DateUtil.beginOfMonth(param.getDateDay());
        DateTime endDate = DateUtil.endOfMonth(param.getDateDay());
        List<VppUser> vppUserList = getUserListByTwoDate(startDate, endDate, vppUserLists);

        List<VppBiddingClearVO.ResolveLoad> resolveLoadList = vppElectricActualService.getListByTenantId(param.getTenantId(), param.getDateDay());
        Map<Long, List<VppBiddingClearVO.ResolveLoad>> resolveLoadMap = resolveLoadList.stream().collect(Collectors.groupingBy(VppBiddingClearVO.ResolveLoad::getUserId));
        List<ElectricityDetailVO> resultList = new ArrayList<>();
        vppUserList.forEach(vppUser -> {
            ElectricityDetailVO electricityDetailVO = new ElectricityDetailVO();
            electricityDetailVO.setName(vppUser.getName());
            List<VppBiddingClearVO.ResolveLoad> list = resolveLoadMap.get(vppUser.getId());
            if (list != null && !list.isEmpty()) {
                electricityDetailVO.setType(1);
                List<BigDecimal> valList = list.stream().map(VppBiddingClearVO.ResolveLoad::getVal).collect(Collectors.toList());
                BigDecimal reduce = valList.stream().reduce(BigDecimal.ZERO, BigDecimal::add);
                valList.add(0, reduce);
                valList.replaceAll(e -> e.setScale(DecimalPlaceEnum.LOAD.length, RoundingMode.HALF_UP));
                List<String> elList = valList.stream().map(BigDecimal::toString).collect(Collectors.toList());
                elList.add(0, DateUtil.format(param.getDateDay(), "yyyy-MM-dd"));
                electricityDetailVO.setDataList(elList);
            } else {
                electricityDetailVO.setType(2);
            }
            electricityDetailVO.setDateDay(param.getDateDay());
            List<String> timeFrameList = new ArrayList<>(Arrays.asList(VppConstant.NINETY_SIX_TIMES));
            timeFrameList.add(0, "日总量");
            timeFrameList.add(0, "日期");
            electricityDetailVO.setTimeFrame(timeFrameList);
            resultList.add(electricityDetailVO);
        });
        return resultList;
    }

    public List<VppUser> getUserListByTwoDate(Date startDate, Date endDate, List<VppUser> listByTenantId) {
        Map<Long, VppUser> vppUserMap = listByTenantId.stream().collect(Collectors.toMap(VppUser::getId, u -> u));
        List<VppUserBindCycle> list = vppBindCycleService.findCycleList(new ArrayList<>(vppUserMap.keySet()));
        Map<Long, VppUser> resultMap = new HashMap<>();
        if (ObjectUtil.isNotEmpty(list)) {
            for (VppUserBindCycle cycle : list) {
                Date bindCycleStart = cycle.getBindCycleStart();
                Date bindCycleEnd = cycle.getBindCycleEnd();
                // 判断日期是否在范围内
                boolean start = DateUtil.isIn(startDate, bindCycleStart, bindCycleEnd);
                boolean end = DateUtil.isIn(endDate, bindCycleStart, bindCycleEnd);
                if (start || end) {
                    if (!resultMap.containsKey(cycle.getUserId())) {
                        VppUser vppUser = vppUserMap.get(cycle.getUserId());
                        resultMap.put(cycle.getUserId(), vppUser);
                    }
                }
            }
        }
        return new ArrayList<>(resultMap.values());
    }

    @Override
    public void delElectric(DataMaintenanceQueryDTO param) {
        //删除
        vppElectricActualConvergeService.removeByParam(param.getDateDay(), param.getTenantId());
        vppElectricActualMiddleService.removeByParam(param.getDateDay(), param.getTenantId());
        vppElectricActualService.removeByParam(param.getDateDay(), param.getTenantId());
        DataMaintenanceUpdateDTO updateDTO = new DataMaintenanceUpdateDTO();
        updateDTO.setTenantId(param.getTenantId());
        updateDTO.setDateDay(param.getDateDay());
        updateDTO.setRealElctricityStatus(2);
        updateDTO.setRealElectricitySources(0);
        dataMaintenanceService.updateStatus(updateDTO);
    }

    @Override
    public void updateStatus(DataMaintenanceUpdateDTO param) {
        LambdaQueryWrapper<VppDataMaintenance> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(VppDataMaintenance::getTenantId, param.getTenantId());
        wrapper.eq(VppDataMaintenance::getDateDay, param.getDateDay());
        VppDataMaintenance vppDataMaintenance = baseMapper.selectOne(wrapper);
        if (vppDataMaintenance != null) {
            if (param.getRealElctricityStatus() != 0) {
                vppDataMaintenance.setRealElctricityStatus(param.getRealElctricityStatus());
            }
            if (param.getDeclareStatus() != 0) {
                vppDataMaintenance.setDeclareStatus(param.getDeclareStatus());
            }
            if (param.getClearResultStatus() != 0) {
                vppDataMaintenance.setClearResultStatus(param.getClearResultStatus());
            }
            if (param.getMediumLongTermStatus() != 0) {
                vppDataMaintenance.setMediumLongTermStatus(param.getMediumLongTermStatus());
            }
            if (ObjectUtil.isNotNull(param.getClearDataSources())) {
                vppDataMaintenance.setClearDataSources(param.getClearDataSources());
            }
            if (ObjectUtil.isNotNull(param.getDeclareSources())) {
                vppDataMaintenance.setDeclareSources(param.getDeclareSources());
            }

            if (ObjectUtil.isNotNull(param.getRealElectricitySources())) {
                vppDataMaintenance.setRealElectricitySources(param.getRealElectricitySources());
            }
            if (ObjectUtil.isNotNull(param.getMediumLongTermSources())) {
                vppDataMaintenance.setMediumLongTermSources(param.getMediumLongTermSources());
            }
            baseMapper.updateById(vppDataMaintenance);
        } else {
            VppDataMaintenance newData = new VppDataMaintenance();
            newData.setTenantId(param.getTenantId());
            newData.setDateDay(param.getDateDay());
            if (param.getRealElctricityStatus() == 0) {
                newData.setRealElctricityStatus(2);
            } else {
                newData.setRealElctricityStatus(param.getRealElctricityStatus());
            }
            if (param.getDeclareStatus() == 0) {
                newData.setDeclareStatus(2);
            } else {
                newData.setDeclareStatus(param.getDeclareStatus());
            }
            if (param.getClearResultStatus() == 0) {
                newData.setClearResultStatus(2);
            } else {
                newData.setClearResultStatus(param.getClearResultStatus());
            }
            if (param.getMediumLongTermStatus() == 0) {
                newData.setMediumLongTermStatus(2);
            } else {
                newData.setMediumLongTermStatus(param.getMediumLongTermStatus());
            }
            if (ObjectUtil.isNotNull(param.getClearDataSources())) {
                newData.setClearDataSources(param.getClearDataSources());
            }
            if (ObjectUtil.isNotNull(param.getDeclareSources())) {
                newData.setDeclareSources(param.getDeclareSources());
            }
            if (ObjectUtil.isNotNull(param.getRealElectricitySources())) {
                newData.setRealElectricitySources(param.getRealElectricitySources());
            }
            if (ObjectUtil.isNotNull(param.getMediumLongTermSources())) {
                newData.setMediumLongTermSources(param.getMediumLongTermSources());
            }
            Date month = DateUtil.parse(DateUtil.format(param.getDateDay(), "yyyy-MM"), "yyyy-MM");
            newData.setDateMonth(month);
            baseMapper.insert(newData);
        }
    }
}
