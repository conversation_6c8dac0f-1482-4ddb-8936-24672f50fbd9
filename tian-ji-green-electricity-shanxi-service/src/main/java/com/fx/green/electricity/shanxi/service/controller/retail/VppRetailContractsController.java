package com.fx.green.electricity.shanxi.service.controller.retail;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryImportRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.VppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 **/
@RestController
@Api(tags = "VppRetailContracts")
@RequestMapping("vppRetailContracts")
public class VppRetailContractsController {

    @Autowired
    private VppRetailContractsService vppRetailContractsService;

    @ApiOperation("添加")
    @CommonNoRepeat
    @PostMapping("add")
    public DataResult<Long> add(@RequestBody VppRetailContractsDTO param) {
        return null;
    }

    @ApiOperation("删除")
    @PostMapping("delete")
    public DataResult<Long> delete(@RequestBody VppRetailContractsDTO param) {
        return null;
    }

    @ApiOperation("修改")
    @PostMapping("edit")
    public DataResult<Long> edit(@RequestBody VppRetailContractsDTO param) {
        return null;
    }

    @ApiOperation("获取零售列表")
    @PostMapping("getList")
    public DataResult<FxPage<VppRetailContractsVO>> getList(@RequestBody QueryVppRetailContractsDTO param) {
        return vppRetailContractsService.getList(param);
    }

    @ApiOperation("零售合同导入")
    @CommonNoRepeat
    @PostMapping("queryImport")
    public DataResult<Void> queryImportRecord(@RequestBody QueryImportRecordDTO queryImportRecordDTO) {
        return vppRetailContractsService.queryImportRecord(queryImportRecordDTO);
    }


}