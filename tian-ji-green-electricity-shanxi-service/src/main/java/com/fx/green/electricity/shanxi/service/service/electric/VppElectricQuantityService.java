package com.fx.green.electricity.shanxi.service.service.electric;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.dto.IdDTO;
import com.fx.common.excel.vo.ImportExcelDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.common.CommonDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DeleteRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.VppElectricQuantityDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.WholesaleAnalysisDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.declare.SeElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.ImportExcelDetailPageListVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricDeclareVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.VppElectricQuantityVO;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricQuantity;

import java.util.List;

/**
 * <AUTHOR>
 **/
public interface VppElectricQuantityService extends IService<VppElectricQuantity> {
    /**
     * 电量数据导入
     *
     * @param param 导入参数
     * @return 结果
     */
    ImportExcelDetailVO importElectric(VppElectricQuantityDTO param,Integer type);


    /**
     * 实际用电量导入
     *
     * @param param 导入参数
     * @return 结果
     */
    ImportExcelDetailVO importElectricNew(List<VppElectricQuantityDTO> param,Integer type);


    /**
     * 根据时间和类型查询详情
     *
     * @param param 时间、类型
     * @return 查询结果
     */
    VppElectricQuantityVO findByTimeAndType(VppElectricQuantityDTO param);

    /**
     * 根据id删除
     *
     * @param param id
     */
    void delete(IdDTO param);

    /**
     * 获取导入分页列表
     *
     * @param param
     * @return
     */
    FxPage<ImportExcelDetailPageListVO> queryImportRecord(VppElectricQuantityDTO.QueryDTO param);

    /**
     * 申报电量导入
     *
     * @param param
     * @return
     */
    DataResult<ImportExcelDetailVO> importRecord(VppElectricQuantityDTO param,Integer type);

    /**
     * 获取申报电量 通过vppId
     *
     * @param param
     */
    List<VppElectricDeclareVO> getAllReportElectricity(WholesaleAnalysisDTO param);

    /**
     * 删除莫一天的申报数据
     *
     * @param deleteRecordDTO
     * @return
     */
    DataResult<Void> deleteRecordData(DeleteRecordDTO deleteRecordDTO);

    /**
     * 下载日前申报数据
     *
     * @param param
     * @return
     */
    DataResult<List<SeElectricDeclareVO>> downloadRecord(CommonDTO.DateDTO param);
}
