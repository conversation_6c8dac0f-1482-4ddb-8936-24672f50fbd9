package com.fx.green.electricity.shanxi.service.service.electric;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.excel.vo.ImportExcelDetailVO;
import com.fx.green.electricity.shanxi.api.pojo.dto.data.DataMaintenanceUpdateDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.electric.*;
import com.fx.green.electricity.shanxi.api.pojo.vo.electric.*;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricActual;
import com.fx.green.electricity.shanxi.service.entity.electric.VppElectricQuantity;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 **/
public interface VppElectricActualService extends IService<VppElectricActual> {
    /**
     * 实际申报电量数据
     *
     * @param vppElectricActualDTOList 实际电量数据
     * @param vppElectricQuantity      电量数据
     * @return 导入结果
     */
    ImportExcelDetailVO importVppElectricActualList(List<VppElectricActualDTO> vppElectricActualDTOList, VppElectricQuantity vppElectricQuantity);

    /**
     * 删除表旧数据
     *
     * @param electricId 电量id
     */
    void deleteByElectricId(Long electricId);

    /**
     * 批量插入实际用电量
     *
     * @param eleList 实际电量列表
     */
    void insertBatch(List<VppElectricActual> eleList);

    /**
     * 获取用户的实际用电量(某一天)
     *
     * @param param
     * @param dateDay
     * @param startTime
     * @param endTime
     * @return
     */
    List<VppBiddingClearVO.ResolveLoad> getList(ResultAnalysisDTO param, Date dateDay, String startTime, String endTime);

    /**
     * 获取用户的实际用电量(时间段)
     *
     * @param param
     * @param startTime
     * @param endTime
     * @return
     */
    List<VppElectricActualVO> getUserRealityPower(ResultAnalysisDTO param, String startTime, String endTime);

    /**
     * 获取实时电量数据 通过用户编码 和 日期 和 时间聚合
     *
     * @param param 电量数据参数
     * @return 实时电量数据列表
     */
    List<VppElectricActualSimpleVO> electricListByDateTimeUser(VppBatteryInformationDTO param);

    /**
     * 获取时间段内的全部数据
     *
     * @param param
     * @param startTime
     * @param endTime
     * @return
     */
    List<VppElectricActualVO> electricActualList(ResultAnalysisDTO param, String startTime, String endTime);

    /**
     * 获取时间段内的数据
     *
     * @param param
     * @param userId
     * @return
     */
    List<PieChartOfUserVO.RatioVO> getAllInfo(ControllableLoadDTO param, Long userId, List<Long> userIdList);

    /**
     * 获取时段的电量总和
     *
     * @param param
     * @return
     */
    BigDecimal getAllPower(ControllableLoadDTO param, List<Long> userIdList);

    /**
     * 获取用户的实际用电量
     *
     * @param userId
     * @param monthStart
     * @param monthEnd
     * @return
     */
    List<VppElectricActualForUserVO> getElectricActual(Long userId, Date monthStart, Date monthEnd);


    /**
     * 获取时间段内的全部实际用电量  多个用户  可以分时段
     *
     * @param param
     * @param userId
     * @return
     */
    List<VppElectricActualVO> electricActualAllList(ResultAnalysisDTO param, List<Long> userId, String startTime, String endTime);

    /**
     * 通过tenantId 获取某一天的全部数据
     *
     * @param resultAnalysisDTO
     * @return
     */
    List<DividendVO.UserElectricSqlVO> electricActualAll(ResultAnalysisDTO resultAnalysisDTO);

    /**
     * 获取实际用电量更新时间
     *
     * @return
     */
    String getUpdateTime(GetUpdateTimeDTO param);

    /**
     * 获取多天的用户的实际用电量
     *
     * @param param
     * @param startTime
     * @param endTime
     * @return
     */
    List<VppBiddingClearVO.ResolveLoad> getElecList(ResultAnalysisDTO param, String startTime, String endTime);

    /**
     * 获取多天的全部用户的数据
     *
     * @param resultAnalysisDTO
     * @param startTime
     * @param endTime
     * @return
     */
    List<VppBiddingClearVO.ResolveLoad> getElecAllList(ResultAnalysisDTO resultAnalysisDTO, String startTime,
                                                       String endTime, List<Long> getAllUserId, List<String> periodDetails);

    /**
     * @param tenantId
     * @param dateDay
     */
    List<VppBiddingClearVO.ResolveLoad> getListByTenantId(Long tenantId, Date dateDay);

    void removeByParam(Date dateDay, Long tenantId);

    /**
     * 获取24小时用户的实际用电量
     *
     * @param startDay
     * @param endDay
     * @param tenantId
     * @param userCodes
     * @return
     */
    Map<String, List<BigDecimal>> getActualTwentyFourForUser(Date startDay, Date endDay, Long tenantId, List<String> userCodes);

    /**
     * 批量添加实际用电量
     * @param allActualDTOs
     * @return
     */
    ImportExcelDetailVO importVppElectricActualListBatch(List<VppElectricActualDTO> allActualDTOs, List<VppElectricQuantityDTO> params, List<DataMaintenanceUpdateDTO> updateDTOs);

    List<VppElectricActual> getElectricByDateList(@NotNull(message = "开始日期不能为空") Date startTime, @NotNull(message = "结束日期不能为空") Date endTime, Long tenantId, String consNo);
}
