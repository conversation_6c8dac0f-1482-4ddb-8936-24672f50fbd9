package com.fx.green.electricity.shanxi.service.service.retail.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryImportRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import com.fx.green.electricity.shanxi.service.constant.VppConstant;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContracts;
import com.fx.green.electricity.shanxi.service.mapper.retail.VppRetailContractsMapper;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsManageService;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 **/
@Service
public class VppRetailContractsServiceImpl extends ServiceImpl<VppRetailContractsMapper, VppRetailContracts> implements VppRetailContractsService {

    @Resource
    private VppRetailContractsManageService vppRetailContractsManageService;


    @Override
    public DataResult<FxPage<VppRetailContractsVO>> getList(QueryVppRetailContractsDTO param) {
        //获取开始结束时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate start = LocalDate.parse(DateUtil.formatDate(param.getStartDate()), formatter);
        LocalDate end = LocalDate.parse(DateUtil.formatDate(param.getEndDate()), formatter);
        List<String> allMonthList = new ArrayList<>();
        YearMonth startYearMonth = YearMonth.from(start);
        YearMonth endYearMonth = YearMonth.from(end);
        while (startYearMonth.isBefore(endYearMonth) || startYearMonth.equals(endYearMonth)) {
            allMonthList.add(startYearMonth.atDay(1).format(formatter));
            startYearMonth = startYearMonth.plusMonths(1);
        }
        for (String s : allMonthList) {
            LambdaQueryWrapper<VppRetailContracts> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VppRetailContracts::getTenantId, param.getTenantId());
            queryWrapper.ge(ObjectUtil.isNotNull(param.getStartDate()), VppRetailContracts::getRunMonth, s);
            queryWrapper.le(ObjectUtil.isNotNull(param.getEndDate()), VppRetailContracts::getRunMonth, s);
            queryWrapper.eq(VppRetailContracts::getIsDelete, VppConstant.ZERO_NUMBER);
            List<VppRetailContracts> vppRetailContractsList = baseMapper.selectList(queryWrapper);
            if (vppRetailContractsList.size() == 0) {
                VppRetailContracts vppRetailContracts = new VppRetailContracts();
                vppRetailContracts.setStatus(0);
                vppRetailContracts.setRunMonth(s);
                vppRetailContracts.setDataItem("零售合同");
                vppRetailContracts.setIsDelete(0);
                vppRetailContracts.setTenantId(param.getTenantId());
                save(vppRetailContracts);

            }
        }
        LambdaQueryWrapper<VppRetailContracts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppRetailContracts::getTenantId, param.getTenantId());
        queryWrapper.ge(ObjectUtil.isNotNull(param.getStartDate()), VppRetailContracts::getRunMonth, DateUtil.formatDate(param.getStartDate()));
        queryWrapper.le(ObjectUtil.isNotNull(param.getEndDate()), VppRetailContracts::getRunMonth, DateUtil.formatDate(param.getEndDate()));
        queryWrapper.eq(ObjectUtil.isNotNull(param.getStatus()), VppRetailContracts::getStatus, param.getStatus());
        queryWrapper.eq(ObjectUtil.isNotNull(param.getTenantId()), VppRetailContracts::getTenantId, param.getTenantId());
        queryWrapper.eq(VppRetailContracts::getIsDelete, VppConstant.ZERO_NUMBER);
        queryWrapper.orderBy(true, true, VppRetailContracts::getId);
        List<VppRetailContracts> list = baseMapper.selectList(queryWrapper);
        List<VppRetailContractsVO> vppRetailContractsDTOS = BeanUtil.copyToList(list, VppRetailContractsVO.class);
        FxPage fxPage = this.ListPage(param, vppRetailContractsDTOS);
        return DataResult.success(fxPage);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResult<Void> queryImportRecord(QueryImportRecordDTO queryImportRecordDTO) {
        //维护文件列表
        UpdateWrapper<VppRetailContracts> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(VppRetailContracts::getAnnex, queryImportRecordDTO.getName());
        updateWrapper.lambda().set(VppRetailContracts::getUrl, queryImportRecordDTO.getUrl());
        updateWrapper.lambda().set(VppRetailContracts::getStatus, 1);
        updateWrapper.lambda().set(VppRetailContracts::getImportDate, new Date());
        updateWrapper.lambda().eq(VppRetailContracts::getId, queryImportRecordDTO.getRetailContractsId());
        this.update(updateWrapper);
        //文件导入入库
        List<VppRetailContractsManageVO> resultList = queryImportRecordDTO.getList();
        return vppRetailContractsManageService.addList(resultList, queryImportRecordDTO);
    }

    public FxPage ListPage(QueryVppRetailContractsDTO param, List data) {
        Integer pageNum = param.getPage();
        Integer pageSize = param.getPageSize();
        // 记录总数
        Integer count = data.size();
        // 页数
        int pageCount;
        if (count % pageSize == 0) {
            pageCount = count / pageSize;
        } else {
            pageCount = count / pageSize + 1;
        }
        // 开始索引
        int fromIndex;
        // 结束索引
        int toIndex;
        if (!pageNum.equals(pageCount)) {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = fromIndex + pageSize;
        } else {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = count;
        }
        List pageList = new ArrayList<>();
        if (data.size() != 0) {
            pageList = data.subList(fromIndex, toIndex);
        }
        return FxPage.page(pageList, count, pageNum, pageSize);
    }

}
