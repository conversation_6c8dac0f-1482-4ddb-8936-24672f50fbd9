package com.fx.green.electricity.shanxi.service.service.retail.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.common.exception.FxServiceException;
import com.fx.common.util.RequestHeadersUtil;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryImportRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.AdjustDeclareDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.file.VppFileUploadVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsManageVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppLoadUserVO;
import com.fx.green.electricity.shanxi.api.utils.FileTypeUtil;
import com.fx.green.electricity.shanxi.service.constant.VppConstant;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContracts;
import com.fx.green.electricity.shanxi.service.mapper.retail.VppRetailContractsMapper;
import com.fx.green.electricity.shanxi.service.service.file.VppFileService;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsManageService;
import com.fx.green.electricity.shanxi.service.service.retail.VppRetailContractsService;
import com.fx.green.electricity.shanxi.service.service.user.VppLoadUserService;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.InputStream;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.YearMonth;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 零售合同管理 Service 实现
 *
 * <AUTHOR>
 **/
@Service
public class VppRetailContractsServiceImpl extends ServiceImpl<VppRetailContractsMapper, VppRetailContracts> implements VppRetailContractsService {

    @Resource
    private VppRetailContractsManageService vppRetailContractsManageService;

    @Resource
    private VppFileService vppFileService;

    @Resource
    private VppLoadUserService vppLoadUserService;


    @Override
    public DataResult<FxPage<VppRetailContractsVO>> getList(QueryVppRetailContractsDTO param) {
        //获取开始结束时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate start = LocalDate.parse(DateUtil.formatDate(param.getStartDate()), formatter);
        LocalDate end = LocalDate.parse(DateUtil.formatDate(param.getEndDate()), formatter);
        List<String> allMonthList = new ArrayList<>();
        YearMonth startYearMonth = YearMonth.from(start);
        YearMonth endYearMonth = YearMonth.from(end);
        while (startYearMonth.isBefore(endYearMonth) || startYearMonth.equals(endYearMonth)) {
            allMonthList.add(startYearMonth.atDay(1).format(formatter));
            startYearMonth = startYearMonth.plusMonths(1);
        }
        for (String s : allMonthList) {
            LambdaQueryWrapper<VppRetailContracts> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(VppRetailContracts::getTenantId, param.getTenantId());
            queryWrapper.ge(ObjectUtil.isNotNull(param.getStartDate()), VppRetailContracts::getRunMonth, s);
            queryWrapper.le(ObjectUtil.isNotNull(param.getEndDate()), VppRetailContracts::getRunMonth, s);
            queryWrapper.eq(VppRetailContracts::getIsDelete, VppConstant.ZERO_NUMBER);
            List<VppRetailContracts> vppRetailContractsList = baseMapper.selectList(queryWrapper);
            if (vppRetailContractsList.isEmpty()) {
                VppRetailContracts vppRetailContracts = new VppRetailContracts();
                vppRetailContracts.setStatus(0);
                vppRetailContracts.setRunMonth(s);
                vppRetailContracts.setDataItem("零售合同");
                vppRetailContracts.setIsDelete(0);
                vppRetailContracts.setTenantId(param.getTenantId());
                save(vppRetailContracts);

            }
        }
        LambdaQueryWrapper<VppRetailContracts> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(VppRetailContracts::getTenantId, param.getTenantId());
        queryWrapper.ge(ObjectUtil.isNotNull(param.getStartDate()), VppRetailContracts::getRunMonth, DateUtil.formatDate(param.getStartDate()));
        queryWrapper.le(ObjectUtil.isNotNull(param.getEndDate()), VppRetailContracts::getRunMonth, DateUtil.formatDate(param.getEndDate()));
        queryWrapper.eq(ObjectUtil.isNotNull(param.getStatus()), VppRetailContracts::getStatus, param.getStatus());
        queryWrapper.eq(ObjectUtil.isNotNull(param.getTenantId()), VppRetailContracts::getTenantId, param.getTenantId());
        queryWrapper.eq(VppRetailContracts::getIsDelete, VppConstant.ZERO_NUMBER);
        queryWrapper.orderBy(true, true, VppRetailContracts::getId);
        List<VppRetailContracts> list = baseMapper.selectList(queryWrapper);
        List<VppRetailContractsVO> vppRetailContractsDTOS = BeanUtil.copyToList(list, VppRetailContractsVO.class);
        FxPage fxPage = this.ListPage(param, vppRetailContractsDTOS);
        return DataResult.success(fxPage);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public DataResult<Void> queryImportRecord(QueryImportRecordDTO queryImportRecordDTO) {
        //维护文件列表
        UpdateWrapper<VppRetailContracts> updateWrapper = new UpdateWrapper<>();
        updateWrapper.lambda().set(VppRetailContracts::getAnnex, queryImportRecordDTO.getName());
        updateWrapper.lambda().set(VppRetailContracts::getUrl, queryImportRecordDTO.getUrl());
        updateWrapper.lambda().set(VppRetailContracts::getStatus, 1);
        updateWrapper.lambda().set(VppRetailContracts::getImportDate, new Date());
        updateWrapper.lambda().eq(VppRetailContracts::getId, queryImportRecordDTO.getRetailContractsId());
        this.update(updateWrapper);
        //文件导入入库
        List<VppRetailContractsManageVO> resultList = queryImportRecordDTO.getList();
        return vppRetailContractsManageService.addList(resultList, queryImportRecordDTO);
    }

    @Override
    public DataResult<Void> importRetailContract(MultipartFile file, String runMonth) {
        // 校验文件类型
        boolean checkExcelFileFlag = FileTypeUtil.checkExcelFile(file);
        if (!checkExcelFileFlag) {
            throw new FxServiceException("请上传xls或xlsx类型文件");
        }

        Long tenantId = RequestHeadersUtil.getRequestHeaders().getTenantId();
        // 获取虚拟电厂当前月份的零售合同id
        QueryVppRetailContractsDTO param = new QueryVppRetailContractsDTO();
        param.setTenantId(tenantId);
        param.setStartDate(DateUtil.parseDate(runMonth + "-01"));
        param.setEndDate(DateUtil.endOfMonth(DateUtil.parseDate(runMonth + "-01")));
        DataResult<FxPage<VppRetailContractsVO>> list = getList(param);
        List<VppRetailContractsVO> records = list.getData().getRecords();

        // 获取本月绑定周期的用户
        AdjustDeclareDTO.QueryUserTreeDTO userTreeDTO = new AdjustDeclareDTO.QueryUserTreeDTO();
        userTreeDTO.setQueryDate(DateUtil.parseDate(runMonth + "-01"));
        userTreeDTO.setTenantId(tenantId);
        Map<String, List<VppLoadUserVO.TreeUserVO>> nameMap = new HashMap<>();
        VppLoadUserVO.TreeVO data = vppLoadUserService.queryTreeList(userTreeDTO);
        if (ObjectUtil.isNotNull(data)) {
            List<VppLoadUserVO.TreeUserVO> treeUserVOList = data.getTreeUserVOList();
            nameMap = treeUserVOList.stream().collect(Collectors.groupingBy(VppLoadUserVO.TreeUserVO::getName));
        }


        if (ObjectUtil.isNotEmpty(records)) {
            try {
                InputStream inputStreams = file.getInputStream();
                LinkedHashMap<String, Integer> listMap = new LinkedHashMap<>();
                List<LinkedHashMap<Integer, String>> excelList = EasyExcelFactory.read(inputStreams).headRowNumber(0).doReadAllSync();
                for (int i = 0; i < 1; i++) {
                    LinkedHashMap<Integer, String> map = excelList.get(i);
                    for (Map.Entry<Integer, String> entry : map.entrySet()) {
                        Integer key = entry.getKey();
                        String s = map.get(key);
                        if (s.equals("电力用户名称") || s.equals("月份") || s.equals("状态") || s.equals("商品名称")
                                || s.equals("是否分时") || s.equals("时段")) {
                            listMap.put(s, key);
                        } else if (s.equals("基础价格套餐/基准套餐")) {
                            listMap.put("价格值名称", key);
                            listMap.put("价格值", key + 1);
                            listMap.put("价差值", key + 2);
                            break;
                        }
                    }
                }

                List<VppRetailContractsManageVO> dataList = new ArrayList<>();
                for (int i = 3; i < excelList.size(); i++) {
                    VppRetailContractsManageVO vppRetailContractsManageVO = new VppRetailContractsManageVO();
                    LinkedHashMap<Integer, String> map = excelList.get(i);

                    Integer status = listMap.get("状态");
                    if (!map.get(status).equals("已解约")) {
                        Integer isTimeSharing = listMap.get("是否分时");
                        if (map.get(isTimeSharing).equals("是")) {
                            Integer nameNumber = listMap.get("电力用户名称");
                            String name = map.get(nameNumber);
                            if (ObjectUtil.isNotEmpty(nameMap.get(name))) {
                                vppRetailContractsManageVO.setCycle(1);
                                vppRetailContractsManageVO.setRetailContractsId(records.get(0).getId());
                                vppRetailContractsManageVO.setSigningMonth(DateUtil.parseDate(runMonth + "-01"));
                                vppRetailContractsManageVO.setType(2);
                                vppRetailContractsManageVO.setName(name);
                                vppRetailContractsManageVO.setUserId(nameMap.get(name).get(0).getId());
                                vppRetailContractsManageVO.setPriceName(map.get(listMap.get("价格值名称")));
                                if (!map.get(listMap.get("价差值")).equals("-")) {
                                    vppRetailContractsManageVO.setPriceDifference(new BigDecimal(map.get(listMap.get("价差值"))));
                                }
                                if (!map.get(listMap.get("价格值")).equals("-")) {
                                    vppRetailContractsManageVO.setVal(new BigDecimal(map.get(listMap.get("价格值"))));
                                }
                                vppRetailContractsManageVO.setTradeName(map.get(listMap.get("商品名称")));
                                vppRetailContractsManageVO.setTenantId(tenantId);
                                Integer periodNumber = listMap.get("时段");
                                String period = map.get(periodNumber);
                                String twentyFourTime = com.fx.green.electricity.shanxi.api.constant.VppConstant.TWENTY_FOUR_TIMES[Integer.parseInt(period) - 1];
                                vppRetailContractsManageVO.setTimeFrame(twentyFourTime);
                                dataList.add(vppRetailContractsManageVO);
                            }
                        }
                    }
                }
                Map<String, List<VppRetailContractsManageVO>> userList = dataList.stream().collect(Collectors.groupingBy(VppRetailContractsManageVO::getName));
                if (nameMap.size() != userList.size()) {
                    return DataResult.failed("导入失败，零售合同用户名称不符，请修改后重试！");
                }
                //上传文件
                DataResult<VppFileUploadVO> fileUploadVODataResult = vppFileService.uploadFile(file);
                String name = fileUploadVODataResult.getData().getName();
                String url = fileUploadVODataResult.getData().getUrl();

                QueryImportRecordDTO queryImportRecordDTO = new QueryImportRecordDTO();
                queryImportRecordDTO.setList(dataList);
                queryImportRecordDTO.setName(name);
                queryImportRecordDTO.setUrl(url);
                queryImportRecordDTO.setRetailContractsId(records.get(0).getId());
                queryImportRecordDTO.setTenantId(tenantId);


                return queryImportRecord(queryImportRecordDTO);
            } catch (
                    Exception e) {
                log.error("导入模板不正确" + e);
                return DataResult.failed("导入模板不正确");
            }
        } else {
            return DataResult.failed("虚拟电厂不存在");
        }
    }

    public FxPage ListPage(QueryVppRetailContractsDTO param, List data) {
        Integer pageNum = param.getPage();
        Integer pageSize = param.getPageSize();
        // 记录总数
        Integer count = data.size();
        // 页数
        int pageCount;
        if (count % pageSize == 0) {
            pageCount = count / pageSize;
        } else {
            pageCount = count / pageSize + 1;
        }
        // 开始索引
        int fromIndex;
        // 结束索引
        int toIndex;
        if (!pageNum.equals(pageCount)) {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = fromIndex + pageSize;
        } else {
            fromIndex = (pageNum - 1) * pageSize;
            toIndex = count;
        }
        List pageList = new ArrayList<>();
        if (data.size() != 0) {
            pageList = data.subList(fromIndex, toIndex);
        }
        return FxPage.page(pageList, count, pageNum, pageSize);
    }

}
