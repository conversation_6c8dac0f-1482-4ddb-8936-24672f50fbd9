package com.fx.green.electricity.shanxi.service.service.retail;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fx.common.constant.DataResult;
import com.fx.common.constant.FxPage;
import com.fx.green.electricity.shanxi.service.entity.retail.VppRetailContracts;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryImportRecordDTO;
import com.fx.green.electricity.shanxi.api.pojo.dto.retail.QueryVppRetailContractsDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.retail.VppRetailContractsVO;

/**
 * <AUTHOR>
 **/
public interface VppRetailContractsService extends IService<VppRetailContracts> {

    /**
     * 获取零售列表
     *
     * @param param
     * @return
     */
    DataResult<FxPage<VppRetailContractsVO>> getList(QueryVppRetailContractsDTO param);

    /**
     * 导入零售合同
     *
     * @param queryImportRecordDTO
     * @return
     */
    DataResult<Void> queryImportRecord(QueryImportRecordDTO queryImportRecordDTO);
}
