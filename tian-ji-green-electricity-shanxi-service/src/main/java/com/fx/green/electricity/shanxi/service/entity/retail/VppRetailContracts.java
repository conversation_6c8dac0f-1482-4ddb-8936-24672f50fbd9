package com.fx.green.electricity.shanxi.service.entity.retail;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fx.green.electricity.shanxi.service.entity.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
*
**/
@Data
@ApiModel("零售合同表")
public class VppRetailContracts extends BaseEntity implements Serializable {

    private static final long serialVersionUID = 1438730367736099159L;
    @ApiModelProperty("主键")
    private Long id;
    @ApiModelProperty("运行月份")
    @JsonFormat(pattern = "yyyy-MM")
    private String runMonth;
    @ApiModelProperty("数据项")
    private String dataItem;
    @ApiModelProperty("0未导入 1导入")
    private Integer status;
    @ApiModelProperty("导入时间")
    private Date importDate;
    @ApiModelProperty("附件")
    private String annex;
    @ApiModelProperty("附件地址")
    private String url;
    @ApiModelProperty("租户id")
    private Long tenantId;


}