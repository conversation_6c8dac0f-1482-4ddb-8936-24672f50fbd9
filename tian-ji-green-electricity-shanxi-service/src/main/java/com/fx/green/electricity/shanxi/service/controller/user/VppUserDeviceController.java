package com.fx.green.electricity.shanxi.service.controller.user;

import com.fx.common.annoation.CommonNoRepeat;
import com.fx.common.constant.DataResult;
import com.fx.green.electricity.shanxi.api.pojo.dto.user.VppUserDeviceAndMeterDTO;
import com.fx.green.electricity.shanxi.api.pojo.vo.user.VppUserDeviceAndMeterVO;
import com.fx.green.electricity.shanxi.service.service.user.VppUserDeviceService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户设备及电表管理 Controller
 *
 * <AUTHOR>
 */
@RestController
@Api(tags = "负荷用户 - 用户设备及电表管理")
@RequestMapping("/vppUserDevice")
public class VppUserDeviceController {

    @Resource
    private VppUserDeviceService vppUserDeviceService;

    @CommonNoRepeat
    @ApiOperation("保存或修改")
    @PostMapping("/saveOrUpdate")
    public DataResult<Void> saveOrUpdate(@RequestBody VppUserDeviceAndMeterDTO param) {
        vppUserDeviceService.saveOrUpdate(param);
        return DataResult.success();
    }

    @ApiOperation("根据用户户号查询用户设备及电表")
    @PostMapping("/getUserDeviceAndMeterByConsNo")
    public DataResult<VppUserDeviceAndMeterVO> getUserDeviceAndMeterByConsNo(@RequestBody VppUserDeviceAndMeterDTO.VppUserDeviceDTO param) {
        return DataResult.success(vppUserDeviceService.getUserDeviceAndMeterByConsNo(param));
    }

}
