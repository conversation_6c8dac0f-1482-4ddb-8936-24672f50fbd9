<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fx.green.electricity.shanxi.service.mapper.data.GeFileRecordMapper">

    <select id="getFileRecordByUnit" resultType="com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO">
        SELECT gu."id" unit_id,
               gu.unit_name,
               gt.tenant_name,
               gf.data_date
        FROM "ge_file_record" gf
                 RIGHT JOIN ge_unit_basic gu ON gu."id" = gf.belong_id
                 LEFT JOIN ge_tenant gt ON gt."id" = gu.tenant_id
            ${ew.customSqlSegment}
    </select>
    <select id="getFileRecordByTenant" resultType="com.fx.green.electricity.shanxi.api.pojo.vo.data.GeFileRecordVO">
        SELECT gt."id" tenant_id,
               gt.tenant_name,
               gf.data_date
        FROM "ge_file_record" gf
                 RIGHT JOIN ge_tenant gt ON gt."id" = gf.belong_id
            ${ew.customSqlSegment}
    </select>
</mapper>